// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type KineticProfile struct {
	Id               int64   `json:"id"`
	Name             string  `json:"name"`
	MaxStaminaMul    float32 `json:"maxStaminaMul"`
	MinRelativeForce float32 `json:"minRelativeForce"`
	BaseForceCoeff   float32 `json:"baseForceCoeff"`
}

var lockKineticProfile sync.RWMutex
var storeKineticProfile sync.Map
var strKineticProfile string = "kinetic_profile"

func InitKineticProfileCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strKineticProfile, watchKineticProfileFunc)
	return LoadAllKineticProfileCfg()
}

func fixKeyKineticProfile(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strKineticProfile)
}
func watchKineticProfileFunc(key string, js string) {
	mapKineticProfile := make(map[int64]*KineticProfile)
	errUnmarshal := json.Unmarshal([]byte(js), &mapKineticProfile)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeKineticProfile.Store(key, mapKineticProfile)
}

func GetAllKineticProfile(option ...consulconfig.Option) map[int64]*KineticProfile {
	fitKey := fixKeyKineticProfile(option...)
	store, ok := storeKineticProfile.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*KineticProfile)
		if ok {
			return storeMap
		}
	}
	lockKineticProfile.Lock()
	defer lockKineticProfile.Unlock()
	store, ok = storeKineticProfile.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*KineticProfile)
		if ok {
			return storeMap
		}
	}
	tblKineticProfile := make(map[int64]*KineticProfile)
	kinetic_profile_str, err := consulconfig.GetInstance().GetConfig(strKineticProfile, option...)
	if kinetic_profile_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(kinetic_profile_str), &tblKineticProfile)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "kinetic_profile", errUnmarshal)
		return nil
	}
	storeKineticProfile.Store(fitKey, tblKineticProfile)
	return tblKineticProfile
}

func GetKineticProfile(id int64, option ...consulconfig.Option) *KineticProfile {
	fitKey := fixKeyKineticProfile(option...)
	store, ok := storeKineticProfile.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*KineticProfile)
		if ok {
			return storeMap[id]
		}
	}
	lockKineticProfile.Lock()
	defer lockKineticProfile.Unlock()
	store, ok = storeKineticProfile.Load(fitKey)
	if ok {
		tblKineticProfile, ok := store.(*KineticProfile)
		if ok {
			return tblKineticProfile
		}
	}
	tblKineticProfile := make(map[int64]*KineticProfile)
	kinetic_profile_str, err := consulconfig.GetInstance().GetConfig(strKineticProfile, option...)
	if kinetic_profile_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(kinetic_profile_str), &tblKineticProfile)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "kinetic_profile", errUnmarshal)
		return nil
	}
	storeKineticProfile.Store(fitKey, tblKineticProfile)
	return tblKineticProfile[id]
}

func LoadAllKineticProfileCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strKineticProfile, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "KineticProfile", successChannels)
	return nil
}

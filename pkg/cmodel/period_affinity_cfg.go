// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PeriodAffinity struct {
	Id                   int64   `json:"id"`
	PeriodGroup          int64   `json:"periodGroup"`
	PeriodId             int64   `json:"periodId"`
	PeriodActivityFactor float64 `json:"periodActivityFactor"`
}

var lockPeriodAffinity sync.RWMutex
var storePeriodAffinity sync.Map
var strPeriodAffinity string = "period_affinity"

func InitPeriodAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPeriodAffinity, watchPeriodAffinityFunc)
	return LoadAllPeriodAffinityCfg()
}

func fixKeyPeriodAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPeriodAffinity)
}
func watchPeriodAffinityFunc(key string, js string) {
	mapPeriodAffinity := make(map[int64]*PeriodAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPeriodAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePeriodAffinity.Store(key, mapPeriodAffinity)
}

func GetAllPeriodAffinity(option ...consulconfig.Option) map[int64]*PeriodAffinity {
	fitKey := fixKeyPeriodAffinity(option...)
	store, ok := storePeriodAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PeriodAffinity)
		if ok {
			return storeMap
		}
	}
	lockPeriodAffinity.Lock()
	defer lockPeriodAffinity.Unlock()
	store, ok = storePeriodAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PeriodAffinity)
		if ok {
			return storeMap
		}
	}
	tblPeriodAffinity := make(map[int64]*PeriodAffinity)
	period_affinity_str, err := consulconfig.GetInstance().GetConfig(strPeriodAffinity, option...)
	if period_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(period_affinity_str), &tblPeriodAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "period_affinity", errUnmarshal)
		return nil
	}
	storePeriodAffinity.Store(fitKey, tblPeriodAffinity)
	return tblPeriodAffinity
}

func GetPeriodAffinity(id int64, option ...consulconfig.Option) *PeriodAffinity {
	fitKey := fixKeyPeriodAffinity(option...)
	store, ok := storePeriodAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PeriodAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockPeriodAffinity.Lock()
	defer lockPeriodAffinity.Unlock()
	store, ok = storePeriodAffinity.Load(fitKey)
	if ok {
		tblPeriodAffinity, ok := store.(*PeriodAffinity)
		if ok {
			return tblPeriodAffinity
		}
	}
	tblPeriodAffinity := make(map[int64]*PeriodAffinity)
	period_affinity_str, err := consulconfig.GetInstance().GetConfig(strPeriodAffinity, option...)
	if period_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(period_affinity_str), &tblPeriodAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "period_affinity", errUnmarshal)
		return nil
	}
	storePeriodAffinity.Store(fitKey, tblPeriodAffinity)
	return tblPeriodAffinity[id]
}

func LoadAllPeriodAffinityCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPeriodAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PeriodAffinity", successChannels)
	return nil
}

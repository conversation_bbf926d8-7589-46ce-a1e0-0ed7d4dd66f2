// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StoreBuy struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	Mark      string `json:"mark"`
	ShowStyle int32  `json:"showStyle"`
	ItemType  int32  `json:"itemType"`
	GoodsId   int64  `json:"goodsId"`
	CostItem  int64  `json:"costItem"`
	CostCount int64  `json:"costCount"`
}

var lockStoreBuy sync.RWMutex
var storeStoreBuy sync.Map
var strStoreBuy string = "store_buy"

func InitStoreBuyCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStoreBuy, watchStoreBuyFunc)
	return LoadAllStoreBuyCfg()
}

func fixKeyStoreBuy(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStoreBuy)
}
func watchStoreBuyFunc(key string, js string) {
	mapStoreBuy := make(map[int64]*StoreBuy)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStoreBuy)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStoreBuy.Store(key, mapStoreBuy)
}

func GetAllStoreBuy(option ...consulconfig.Option) map[int64]*StoreBuy {
	fitKey := fixKeyStoreBuy(option...)
	store, ok := storeStoreBuy.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreBuy)
		if ok {
			return storeMap
		}
	}
	lockStoreBuy.Lock()
	defer lockStoreBuy.Unlock()
	store, ok = storeStoreBuy.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreBuy)
		if ok {
			return storeMap
		}
	}
	tblStoreBuy := make(map[int64]*StoreBuy)
	store_buy_str, err := consulconfig.GetInstance().GetConfig(strStoreBuy, option...)
	if store_buy_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(store_buy_str), &tblStoreBuy)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "store_buy", errUnmarshal)
		return nil
	}
	storeStoreBuy.Store(fitKey, tblStoreBuy)
	return tblStoreBuy
}

func GetStoreBuy(id int64, option ...consulconfig.Option) *StoreBuy {
	fitKey := fixKeyStoreBuy(option...)
	store, ok := storeStoreBuy.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StoreBuy)
		if ok {
			return storeMap[id]
		}
	}
	lockStoreBuy.Lock()
	defer lockStoreBuy.Unlock()
	store, ok = storeStoreBuy.Load(fitKey)
	if ok {
		tblStoreBuy, ok := store.(*StoreBuy)
		if ok {
			return tblStoreBuy
		}
	}
	tblStoreBuy := make(map[int64]*StoreBuy)
	store_buy_str, err := consulconfig.GetInstance().GetConfig(strStoreBuy, option...)
	if store_buy_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(store_buy_str), &tblStoreBuy)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "store_buy", errUnmarshal)
		return nil
	}
	storeStoreBuy.Store(fitKey, tblStoreBuy)
	return tblStoreBuy[id]
}

func LoadAllStoreBuyCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStoreBuy, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StoreBuy", successChannels)
	return nil
}

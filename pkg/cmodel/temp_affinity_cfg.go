// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TempAffinity struct {
	Id                int64   `json:"id"`
	Name              string  `json:"name"`
	TemperatureFav    int32   `json:"temperatureFav"`
	TempAffectedRatio float64 `json:"tempAffectedRatio"`
	TempThreshold     float64 `json:"tempThreshold"`
	Mark              string  `json:"mark"`
}

var lockTempAffinity sync.RWMutex
var storeTempAffinity sync.Map
var strTempAffinity string = "temp_affinity"

func InitTempAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTempAffinity, watchTempAffinityFunc)
	return LoadAllTempAffinityCfg()
}

func fixKeyTempAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTempAffinity)
}
func watchTempAffinityFunc(key string, js string) {
	mapTempAffinity := make(map[int64]*TempAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTempAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTempAffinity.Store(key, mapTempAffinity)
}

func GetAllTempAffinity(option ...consulconfig.Option) map[int64]*TempAffinity {
	fitKey := fixKeyTempAffinity(option...)
	store, ok := storeTempAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TempAffinity)
		if ok {
			return storeMap
		}
	}
	lockTempAffinity.Lock()
	defer lockTempAffinity.Unlock()
	store, ok = storeTempAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TempAffinity)
		if ok {
			return storeMap
		}
	}
	tblTempAffinity := make(map[int64]*TempAffinity)
	temp_affinity_str, err := consulconfig.GetInstance().GetConfig(strTempAffinity, option...)
	if temp_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(temp_affinity_str), &tblTempAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "temp_affinity", errUnmarshal)
		return nil
	}
	storeTempAffinity.Store(fitKey, tblTempAffinity)
	return tblTempAffinity
}

func GetTempAffinity(id int64, option ...consulconfig.Option) *TempAffinity {
	fitKey := fixKeyTempAffinity(option...)
	store, ok := storeTempAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TempAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockTempAffinity.Lock()
	defer lockTempAffinity.Unlock()
	store, ok = storeTempAffinity.Load(fitKey)
	if ok {
		tblTempAffinity, ok := store.(*TempAffinity)
		if ok {
			return tblTempAffinity
		}
	}
	tblTempAffinity := make(map[int64]*TempAffinity)
	temp_affinity_str, err := consulconfig.GetInstance().GetConfig(strTempAffinity, option...)
	if temp_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(temp_affinity_str), &tblTempAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "temp_affinity", errUnmarshal)
		return nil
	}
	storeTempAffinity.Store(fitKey, tblTempAffinity)
	return tblTempAffinity[id]
}

func LoadAllTempAffinityCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTempAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TempAffinity", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type MapSpotSpotList struct {
	SpotId   int32  `json:"spotId"`
	SpotName int64  `json:"spotName"`
	AssetId  string `json:"assetId"`
	X        int32  `json:"x"`
	Y        int32  `json:"y"`
}

type MapSpot struct {
	Id       int64             `json:"id"`
	Name     string            `json:"name"`
	SpotList []MapSpotSpotList `json:"SpotList"`
}

var lockMapSpot sync.RWMutex
var storeMapSpot sync.Map
var strMapSpot string = "map_spot"

func InitMapSpotCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strMapSpot, watchMapSpotFunc)
	return LoadAllMapSpotCfg()
}

func fixKeyMapSpot(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strMapSpot)
}
func watchMapSpotFunc(key string, js string) {
	mapMapSpot := make(map[int64]*MapSpot)
	errUnmarshal := json.Unmarshal([]byte(js), &mapMapSpot)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeMapSpot.Store(key, mapMapSpot)
}

func GetAllMapSpot(option ...consulconfig.Option) map[int64]*MapSpot {
	fitKey := fixKeyMapSpot(option...)
	store, ok := storeMapSpot.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapSpot)
		if ok {
			return storeMap
		}
	}
	lockMapSpot.Lock()
	defer lockMapSpot.Unlock()
	store, ok = storeMapSpot.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapSpot)
		if ok {
			return storeMap
		}
	}
	tblMapSpot := make(map[int64]*MapSpot)
	map_spot_str, err := consulconfig.GetInstance().GetConfig(strMapSpot, option...)
	if map_spot_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_spot_str), &tblMapSpot)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_spot", errUnmarshal)
		return nil
	}
	storeMapSpot.Store(fitKey, tblMapSpot)
	return tblMapSpot
}

func GetMapSpot(id int64, option ...consulconfig.Option) *MapSpot {
	fitKey := fixKeyMapSpot(option...)
	store, ok := storeMapSpot.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapSpot)
		if ok {
			return storeMap[id]
		}
	}
	lockMapSpot.Lock()
	defer lockMapSpot.Unlock()
	store, ok = storeMapSpot.Load(fitKey)
	if ok {
		tblMapSpot, ok := store.(*MapSpot)
		if ok {
			return tblMapSpot
		}
	}
	tblMapSpot := make(map[int64]*MapSpot)
	map_spot_str, err := consulconfig.GetInstance().GetConfig(strMapSpot, option...)
	if map_spot_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_spot_str), &tblMapSpot)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_spot", errUnmarshal)
		return nil
	}
	storeMapSpot.Store(fitKey, tblMapSpot)
	return tblMapSpot[id]
}

func LoadAllMapSpotCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strMapSpot, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "MapSpot", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type GateInfo struct {
	EnableMsgKill bool  `json:"enableMsgKill"`
	WarnLimitSec1 int64 `json:"warnLimitSec1"`
	MsgLimitSec1  int64 `json:"msgLimitSec1"`
	MsgLimitSec3  int64 `json:"msgLimitSec3"`
	MsgKillSleep  int64 `json:"msgKillSleep"`
	IpKillSleep   int64 `json:"ipKillSleep"`
}

var lockGateInfo sync.RWMutex
var storeGateInfo sync.Map
var strGateInfo string = "gate_info"

func InitGateInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strGateInfo, watchGateInfoFunc)
	return LoadAllGateInfoCfg()
}

func fixKeyGateInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strGateInfo)
}
func watchGateInfoFunc(key string, js string) {
	store, ok := storeGateInfo.Load(key)
	if !ok {
		store = &GateInfo{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeGateInfo.Store(key, store)
}

func GetGateInfo(option ...consulconfig.Option) *GateInfo {
	fitKey := fixKeyGateInfo(option...)
	store, ok := storeGateInfo.Load(fitKey)
	if ok {
		tblGateInfo, ok := store.(*GateInfo)
		if ok {
			return tblGateInfo
		}
	}
	lockGateInfo.Lock()
	defer lockGateInfo.Unlock()
	store, ok = storeGateInfo.Load(fitKey)
	if ok {
		tblGateInfo, ok := store.(*GateInfo)
		if ok {
			return tblGateInfo
		}
	}
	tblGateInfo := &GateInfo{}
	gate_info_str, err := consulconfig.GetInstance().GetConfig(strGateInfo, option...)
	if gate_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(gate_info_str), &tblGateInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strGateInfo, errUnmarshal, gate_info_str)
		return nil
	}
	storeGateInfo.Store(fitKey, tblGateInfo)
	return tblGateInfo
}

func LoadAllGateInfoCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strGateInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "GateInfo", successChannels)
	return nil
}

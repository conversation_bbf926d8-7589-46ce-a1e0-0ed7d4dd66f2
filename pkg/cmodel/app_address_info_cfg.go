// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AppAddressInfo struct {
	Id           int64  `json:"id"`
	SrvUri       string `json:"srvUri"`
	CdnHost      string `json:"cdnHost"`
	ResourceUri  string `json:"resourceUri"`
	ConfigUri    string `json:"configUri"`
	FbShareUri   string `json:"fbShareUri"`
	LogUploadUri string `json:"logUploadUri"`
}

var lockAppAddressInfo sync.RWMutex
var storeAppAddressInfo sync.Map
var strAppAddressInfo string = "app_address_info"

func InitAppAddressInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAppAddressInfo, watchAppAddressInfoFunc)
	return LoadAllAppAddressInfoCfg()
}

func fixKeyAppAddressInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAppAddressInfo)
}
func watchAppAddressInfoFunc(key string, js string) {
	store, ok := storeAppAddressInfo.Load(key)
	if !ok {
		store = &AppAddressInfo{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAppAddressInfo.Store(key, store)
}

func GetAppAddressInfo(option ...consulconfig.Option) *AppAddressInfo {
	fitKey := fixKeyAppAddressInfo(option...)
	store, ok := storeAppAddressInfo.Load(fitKey)
	if ok {
		tblAppAddressInfo, ok := store.(*AppAddressInfo)
		if ok {
			return tblAppAddressInfo
		}
	}
	lockAppAddressInfo.Lock()
	defer lockAppAddressInfo.Unlock()
	store, ok = storeAppAddressInfo.Load(fitKey)
	if ok {
		tblAppAddressInfo, ok := store.(*AppAddressInfo)
		if ok {
			return tblAppAddressInfo
		}
	}
	tblAppAddressInfo := &AppAddressInfo{}
	app_address_info_str, err := consulconfig.GetInstance().GetConfig(strAppAddressInfo, option...)
	if app_address_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(app_address_info_str), &tblAppAddressInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strAppAddressInfo, errUnmarshal, app_address_info_str)
		return nil
	}
	storeAppAddressInfo.Store(fitKey, tblAppAddressInfo)
	return tblAppAddressInfo
}

func LoadAllAppAddressInfoCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAppAddressInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AppAddressInfo", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ReelsBaitWeight struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type Reels struct {
	Id               int64           `json:"id"`
	Brand            int32           `json:"brand"`
	Series           int32           `json:"series"`
	SizeName         string          `json:"sizeName"`
	SeriesDes        string          `json:"seriesDes"`
	SubType          int32           `json:"subType"`
	Name             string          `json:"name"`
	Mark             string          `json:"mark"`
	ArtId            string          `json:"artId"`
	Durability       int32           `json:"durability"`
	DurabilityCoeff  int32           `json:"durabilityCoeff"`
	DurabilityTag    int32           `json:"durabilityTag"`
	ModelNum         int32           `json:"modelNum"`
	Drag             int32           `json:"drag"`
	Size             int32           `json:"size"`
	Weight           int32           `json:"weight"`
	WeightCoeff      int32           `json:"weightCoeff"`
	WeightTag        int32           `json:"weightTag"`
	Ratio            float32         `json:"ratio"`
	FrictionCount    int32           `json:"frictionCount"`
	Speed            int32           `json:"speed"`
	Circumference    int32           `json:"circumference"`
	Capacity         int32           `json:"capacity"`
	CapacitySize     int32           `json:"capacitySize"`
	TensionFactor    float32         `json:"tensionFactor"`
	FrictionFactor   float32         `json:"frictionFactor"`
	EnergyCostFactor float32         `json:"energyCostFactor"`
	BaitWeight       ReelsBaitWeight `json:"baitWeight"`
	DistanceCoeff    int32           `json:"distanceCoeff"`
	DistanceTag      int32           `json:"distanceTag"`
	HighSpeedTag     bool            `json:"highSpeedTag"`
}

var lockReels sync.RWMutex
var storeReels sync.Map
var strReels string = "reels"

func InitReelsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strReels, watchReelsFunc)
	return LoadAllReelsCfg()
}

func fixKeyReels(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strReels)
}
func watchReelsFunc(key string, js string) {
	mapReels := make(map[int64]*Reels)
	errUnmarshal := json.Unmarshal([]byte(js), &mapReels)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeReels.Store(key, mapReels)
}

func GetAllReels(option ...consulconfig.Option) map[int64]*Reels {
	fitKey := fixKeyReels(option...)
	store, ok := storeReels.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Reels)
		if ok {
			return storeMap
		}
	}
	lockReels.Lock()
	defer lockReels.Unlock()
	store, ok = storeReels.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Reels)
		if ok {
			return storeMap
		}
	}
	tblReels := make(map[int64]*Reels)
	reels_str, err := consulconfig.GetInstance().GetConfig(strReels, option...)
	if reels_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(reels_str), &tblReels)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "reels", errUnmarshal)
		return nil
	}
	storeReels.Store(fitKey, tblReels)
	return tblReels
}

func GetReels(id int64, option ...consulconfig.Option) *Reels {
	fitKey := fixKeyReels(option...)
	store, ok := storeReels.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Reels)
		if ok {
			return storeMap[id]
		}
	}
	lockReels.Lock()
	defer lockReels.Unlock()
	store, ok = storeReels.Load(fitKey)
	if ok {
		tblReels, ok := store.(*Reels)
		if ok {
			return tblReels
		}
	}
	tblReels := make(map[int64]*Reels)
	reels_str, err := consulconfig.GetInstance().GetConfig(strReels, option...)
	if reels_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(reels_str), &tblReels)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "reels", errUnmarshal)
		return nil
	}
	storeReels.Store(fitKey, tblReels)
	return tblReels[id]
}

func LoadAllReelsCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strReels, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Reels", successChannels)
	return nil
}

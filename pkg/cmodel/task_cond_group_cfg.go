// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskCondGroupOpenConds struct {
	OpenCond int64 `json:"openCond"`
	OpenVal  int64 `json:"openVal"`
}

type TaskCondGroup struct {
	Id          int64                    `json:"id"`
	Name        string                   `json:"name"`
	OpenControl int32                    `json:"openControl"`
	OpenConds   []TaskCondGroupOpenConds `json:"openConds"`
	Conds       []int64                  `json:"conds"`
}

var lockTaskCondGroup sync.RWMutex
var storeTaskCondGroup sync.Map
var strTaskCondGroup string = "task_cond_group"

func InitTaskCondGroupCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskCondGroup, watchTaskCondGroupFunc)
	return LoadAllTaskCondGroupCfg()
}

func fixKeyTaskCondGroup(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskCondGroup)
}
func watchTaskCondGroupFunc(key string, js string) {
	mapTaskCondGroup := make(map[int64]*TaskCondGroup)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskCondGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskCondGroup.Store(key, mapTaskCondGroup)
}

func GetAllTaskCondGroup(option ...consulconfig.Option) map[int64]*TaskCondGroup {
	fitKey := fixKeyTaskCondGroup(option...)
	store, ok := storeTaskCondGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskCondGroup)
		if ok {
			return storeMap
		}
	}
	lockTaskCondGroup.Lock()
	defer lockTaskCondGroup.Unlock()
	store, ok = storeTaskCondGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskCondGroup)
		if ok {
			return storeMap
		}
	}
	tblTaskCondGroup := make(map[int64]*TaskCondGroup)
	task_cond_group_str, err := consulconfig.GetInstance().GetConfig(strTaskCondGroup, option...)
	if task_cond_group_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_cond_group_str), &tblTaskCondGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_cond_group", errUnmarshal)
		return nil
	}
	storeTaskCondGroup.Store(fitKey, tblTaskCondGroup)
	return tblTaskCondGroup
}

func GetTaskCondGroup(id int64, option ...consulconfig.Option) *TaskCondGroup {
	fitKey := fixKeyTaskCondGroup(option...)
	store, ok := storeTaskCondGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskCondGroup)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskCondGroup.Lock()
	defer lockTaskCondGroup.Unlock()
	store, ok = storeTaskCondGroup.Load(fitKey)
	if ok {
		tblTaskCondGroup, ok := store.(*TaskCondGroup)
		if ok {
			return tblTaskCondGroup
		}
	}
	tblTaskCondGroup := make(map[int64]*TaskCondGroup)
	task_cond_group_str, err := consulconfig.GetInstance().GetConfig(strTaskCondGroup, option...)
	if task_cond_group_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_cond_group_str), &tblTaskCondGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_cond_group", errUnmarshal)
		return nil
	}
	storeTaskCondGroup.Store(fitKey, tblTaskCondGroup)
	return tblTaskCondGroup[id]
}

func LoadAllTaskCondGroupCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskCondGroup, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskCondGroup", successChannels)
	return nil
}

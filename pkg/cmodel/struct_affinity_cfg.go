// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StructAffinityList struct {
	StructType int32   `json:"structType"`
	Coeff      float64 `json:"coeff"`
}

type StructAffinity struct {
	Id   int64                `json:"id"`
	Name string               `json:"name"`
	List []StructAffinityList `json:"List"`
	Mark string               `json:"mark"`
}

var lockStructAffinity sync.RWMutex
var storeStructAffinity sync.Map
var strStructAffinity string = "struct_affinity"

func InitStructAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStructAffinity, watchStructAffinityFunc)
	return LoadAllStructAffinityCfg()
}

func fixKeyStructAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStructAffinity)
}
func watchStructAffinityFunc(key string, js string) {
	mapStructAffinity := make(map[int64]*StructAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStructAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStructAffinity.Store(key, mapStructAffinity)
}

func GetAllStructAffinity(option ...consulconfig.Option) map[int64]*StructAffinity {
	fitKey := fixKeyStructAffinity(option...)
	store, ok := storeStructAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StructAffinity)
		if ok {
			return storeMap
		}
	}
	lockStructAffinity.Lock()
	defer lockStructAffinity.Unlock()
	store, ok = storeStructAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StructAffinity)
		if ok {
			return storeMap
		}
	}
	tblStructAffinity := make(map[int64]*StructAffinity)
	struct_affinity_str, err := consulconfig.GetInstance().GetConfig(strStructAffinity, option...)
	if struct_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(struct_affinity_str), &tblStructAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "struct_affinity", errUnmarshal)
		return nil
	}
	storeStructAffinity.Store(fitKey, tblStructAffinity)
	return tblStructAffinity
}

func GetStructAffinity(id int64, option ...consulconfig.Option) *StructAffinity {
	fitKey := fixKeyStructAffinity(option...)
	store, ok := storeStructAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StructAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockStructAffinity.Lock()
	defer lockStructAffinity.Unlock()
	store, ok = storeStructAffinity.Load(fitKey)
	if ok {
		tblStructAffinity, ok := store.(*StructAffinity)
		if ok {
			return tblStructAffinity
		}
	}
	tblStructAffinity := make(map[int64]*StructAffinity)
	struct_affinity_str, err := consulconfig.GetInstance().GetConfig(strStructAffinity, option...)
	if struct_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(struct_affinity_str), &tblStructAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "struct_affinity", errUnmarshal)
		return nil
	}
	storeStructAffinity.Store(fitKey, tblStructAffinity)
	return tblStructAffinity[id]
}

func LoadAllStructAffinityCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStructAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StructAffinity", successChannels)
	return nil
}

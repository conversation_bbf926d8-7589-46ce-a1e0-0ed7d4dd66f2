// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RankRewardAward struct {
	ItemId int64 `json:"itemId"`
	Val    int64 `json:"val"`
}

type RankReward struct {
	Id         int64             `json:"id"`
	Mark       string            `json:"mark"`
	RankId     int64             `json:"rankId"`
	Rank       int32             `json:"rank"`
	TemplateId int64             `json:"templateId"`
	Award      []RankRewardAward `json:"award"`
}

var lockRankReward sync.RWMutex
var storeRankReward sync.Map
var strRankReward string = "rank_reward"

func InitRankRewardCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRankReward, watchRankRewardFunc)
	return LoadAllRankRewardCfg()
}

func fixKeyRankReward(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRankReward)
}
func watchRankRewardFunc(key string, js string) {
	mapRankReward := make(map[int64]*RankReward)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRankReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRankReward.Store(key, mapRankReward)
}

func GetAllRankReward(option ...consulconfig.Option) map[int64]*RankReward {
	fitKey := fixKeyRankReward(option...)
	store, ok := storeRankReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RankReward)
		if ok {
			return storeMap
		}
	}
	lockRankReward.Lock()
	defer lockRankReward.Unlock()
	store, ok = storeRankReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RankReward)
		if ok {
			return storeMap
		}
	}
	tblRankReward := make(map[int64]*RankReward)
	rank_reward_str, err := consulconfig.GetInstance().GetConfig(strRankReward, option...)
	if rank_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rank_reward_str), &tblRankReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rank_reward", errUnmarshal)
		return nil
	}
	storeRankReward.Store(fitKey, tblRankReward)
	return tblRankReward
}

func GetRankReward(id int64, option ...consulconfig.Option) *RankReward {
	fitKey := fixKeyRankReward(option...)
	store, ok := storeRankReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RankReward)
		if ok {
			return storeMap[id]
		}
	}
	lockRankReward.Lock()
	defer lockRankReward.Unlock()
	store, ok = storeRankReward.Load(fitKey)
	if ok {
		tblRankReward, ok := store.(*RankReward)
		if ok {
			return tblRankReward
		}
	}
	tblRankReward := make(map[int64]*RankReward)
	rank_reward_str, err := consulconfig.GetInstance().GetConfig(strRankReward, option...)
	if rank_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rank_reward_str), &tblRankReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rank_reward", errUnmarshal)
		return nil
	}
	storeRankReward.Store(fitKey, tblRankReward)
	return tblRankReward[id]
}

func LoadAllRankRewardCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRankReward, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RankReward", successChannels)
	return nil
}

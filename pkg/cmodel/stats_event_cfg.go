// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StatsEvent struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockStatsEvent sync.RWMutex
var storeStatsEvent sync.Map
var strStatsEvent string = "stats_event"

func InitStatsEventCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStatsEvent, watchStatsEventFunc)
	return LoadAllStatsEventCfg()
}

func fixKeyStatsEvent(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStatsEvent)
}
func watchStatsEventFunc(key string, js string) {
	mapStatsEvent := make(map[int64]*StatsEvent)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStatsEvent)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStatsEvent.Store(key, mapStatsEvent)
}

func GetAllStatsEvent(option ...consulconfig.Option) map[int64]*StatsEvent {
	fitKey := fixKeyStatsEvent(option...)
	store, ok := storeStatsEvent.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsEvent)
		if ok {
			return storeMap
		}
	}
	lockStatsEvent.Lock()
	defer lockStatsEvent.Unlock()
	store, ok = storeStatsEvent.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsEvent)
		if ok {
			return storeMap
		}
	}
	tblStatsEvent := make(map[int64]*StatsEvent)
	stats_event_str, err := consulconfig.GetInstance().GetConfig(strStatsEvent, option...)
	if stats_event_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_event_str), &tblStatsEvent)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_event", errUnmarshal)
		return nil
	}
	storeStatsEvent.Store(fitKey, tblStatsEvent)
	return tblStatsEvent
}

func GetStatsEvent(id int64, option ...consulconfig.Option) *StatsEvent {
	fitKey := fixKeyStatsEvent(option...)
	store, ok := storeStatsEvent.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsEvent)
		if ok {
			return storeMap[id]
		}
	}
	lockStatsEvent.Lock()
	defer lockStatsEvent.Unlock()
	store, ok = storeStatsEvent.Load(fitKey)
	if ok {
		tblStatsEvent, ok := store.(*StatsEvent)
		if ok {
			return tblStatsEvent
		}
	}
	tblStatsEvent := make(map[int64]*StatsEvent)
	stats_event_str, err := consulconfig.GetInstance().GetConfig(strStatsEvent, option...)
	if stats_event_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_event_str), &tblStatsEvent)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_event", errUnmarshal)
		return nil
	}
	storeStatsEvent.Store(fitKey, tblStatsEvent)
	return tblStatsEvent[id]
}

func LoadAllStatsEventCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStatsEvent, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StatsEvent", successChannels)
	return nil
}

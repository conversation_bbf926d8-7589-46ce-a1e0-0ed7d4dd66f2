// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistribute struct {
	Id                 int64  `json:"id"`
	Name               string `json:"name"`
	Species            int64  `json:"species"`
	FishQuality        int64  `json:"fishQuality"`
	FishCharacter      int64  `json:"fishCharacter"`
	FishHabitus        int64  `json:"fishHabitus"`
	PeriodId           int64  `json:"periodId"`
	WaterLayerId       int64  `json:"waterLayerId"`
	WaterTemperatureId int64  `json:"waterTemperatureId"`
	HabitatWaterId     int64  `json:"habitatWaterId"`
	BarrierId          int64  `json:"barrierId"`
	BaitId             int64  `json:"baitId"`
	WeatherId          int64  `json:"weatherId"`
	Mark               string `json:"mark"`
}

var lockFishDistribute sync.RWMutex
var storeFishDistribute sync.Map
var strFishDistribute string = "fish_distribute"

func InitFishDistributeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistribute, watchFishDistributeFunc)
	return LoadAllFishDistributeCfg()
}

func fixKeyFishDistribute(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistribute)
}
func watchFishDistributeFunc(key string, js string) {
	mapFishDistribute := make(map[int64]*FishDistribute)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistribute)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistribute.Store(key, mapFishDistribute)
}

func GetAllFishDistribute(option ...consulconfig.Option) map[int64]*FishDistribute {
	fitKey := fixKeyFishDistribute(option...)
	store, ok := storeFishDistribute.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistribute)
		if ok {
			return storeMap
		}
	}
	lockFishDistribute.Lock()
	defer lockFishDistribute.Unlock()
	store, ok = storeFishDistribute.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistribute)
		if ok {
			return storeMap
		}
	}
	tblFishDistribute := make(map[int64]*FishDistribute)
	fish_distribute_str, err := consulconfig.GetInstance().GetConfig(strFishDistribute, option...)
	if fish_distribute_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_str), &tblFishDistribute)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute", errUnmarshal)
		return nil
	}
	storeFishDistribute.Store(fitKey, tblFishDistribute)
	return tblFishDistribute
}

func GetFishDistribute(id int64, option ...consulconfig.Option) *FishDistribute {
	fitKey := fixKeyFishDistribute(option...)
	store, ok := storeFishDistribute.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistribute)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistribute.Lock()
	defer lockFishDistribute.Unlock()
	store, ok = storeFishDistribute.Load(fitKey)
	if ok {
		tblFishDistribute, ok := store.(*FishDistribute)
		if ok {
			return tblFishDistribute
		}
	}
	tblFishDistribute := make(map[int64]*FishDistribute)
	fish_distribute_str, err := consulconfig.GetInstance().GetConfig(strFishDistribute, option...)
	if fish_distribute_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_str), &tblFishDistribute)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute", errUnmarshal)
		return nil
	}
	storeFishDistribute.Store(fitKey, tblFishDistribute)
	return tblFishDistribute[id]
}

func LoadAllFishDistributeCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDistribute, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistribute", successChannels)
	return nil
}

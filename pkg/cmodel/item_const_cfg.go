// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ItemConst struct {
	TripBagTackleCells      int64 `json:"tripBagTackleCells"`
	TripBagFoodCells        int64 `json:"tripBagFoodCells"`
	TripBagRodRigCells      int64 `json:"tripBagRodRigCells"`
	MaintainMaxReduceFactor int32 `json:"maintainMaxReduceFactor"`
	MaintainReduceFactor    int32 `json:"maintainReduceFactor"`
	TackleSellPriceFactor   int32 `json:"tackleSellPriceFactor"`
}

var lockItemConst sync.RWMutex
var storeItemConst sync.Map
var strItemConst string = "item_const"

func InitItemConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItemConst, watchItemConstFunc)
	return LoadAllItemConstCfg()
}

func fixKeyItemConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItemConst)
}
func watchItemConstFunc(key string, js string) {
	store, ok := storeItemConst.Load(key)
	if !ok {
		store = &ItemConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItemConst.Store(key, store)
}

func GetItemConst(option ...consulconfig.Option) *ItemConst {
	fitKey := fixKeyItemConst(option...)
	store, ok := storeItemConst.Load(fitKey)
	if ok {
		tblItemConst, ok := store.(*ItemConst)
		if ok {
			return tblItemConst
		}
	}
	lockItemConst.Lock()
	defer lockItemConst.Unlock()
	store, ok = storeItemConst.Load(fitKey)
	if ok {
		tblItemConst, ok := store.(*ItemConst)
		if ok {
			return tblItemConst
		}
	}
	tblItemConst := &ItemConst{}
	item_const_str, err := consulconfig.GetInstance().GetConfig(strItemConst, option...)
	if item_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_const_str), &tblItemConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strItemConst, errUnmarshal, item_const_str)
		return nil
	}
	storeItemConst.Store(fitKey, tblItemConst)
	return tblItemConst
}

func LoadAllItemConstCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItemConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ItemConst", successChannels)
	return nil
}

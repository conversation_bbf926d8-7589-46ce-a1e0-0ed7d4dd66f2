// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeWaterLayerWaterLayer struct {
	WaterLayerId int64 `json:"waterLayerId"`
	Weight       int32 `json:"weight"`
}

type FishDistributeWaterLayer struct {
	Id         int64                                `json:"id"`
	Name       string                               `json:"name"`
	WaterLayer []FishDistributeWaterLayerWaterLayer `json:"waterLayer"`
}

var lockFishDistributeWaterLayer sync.RWMutex
var storeFishDistributeWaterLayer sync.Map
var strFishDistributeWaterLayer string = "fish_distribute_water_layer"

func InitFishDistributeWaterLayerCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeWaterLayer, watchFishDistributeWaterLayerFunc)
	return LoadAllFishDistributeWaterLayerCfg()
}

func fixKeyFishDistributeWaterLayer(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeWaterLayer)
}
func watchFishDistributeWaterLayerFunc(key string, js string) {
	mapFishDistributeWaterLayer := make(map[int64]*FishDistributeWaterLayer)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributeWaterLayer)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeWaterLayer.Store(key, mapFishDistributeWaterLayer)
}

func GetAllFishDistributeWaterLayer(option ...consulconfig.Option) map[int64]*FishDistributeWaterLayer {
	fitKey := fixKeyFishDistributeWaterLayer(option...)
	store, ok := storeFishDistributeWaterLayer.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeWaterLayer)
		if ok {
			return storeMap
		}
	}
	lockFishDistributeWaterLayer.Lock()
	defer lockFishDistributeWaterLayer.Unlock()
	store, ok = storeFishDistributeWaterLayer.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeWaterLayer)
		if ok {
			return storeMap
		}
	}
	tblFishDistributeWaterLayer := make(map[int64]*FishDistributeWaterLayer)
	fish_distribute_water_layer_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeWaterLayer, option...)
	if fish_distribute_water_layer_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_water_layer_str), &tblFishDistributeWaterLayer)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_water_layer", errUnmarshal)
		return nil
	}
	storeFishDistributeWaterLayer.Store(fitKey, tblFishDistributeWaterLayer)
	return tblFishDistributeWaterLayer
}

func GetFishDistributeWaterLayer(id int64, option ...consulconfig.Option) *FishDistributeWaterLayer {
	fitKey := fixKeyFishDistributeWaterLayer(option...)
	store, ok := storeFishDistributeWaterLayer.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeWaterLayer)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributeWaterLayer.Lock()
	defer lockFishDistributeWaterLayer.Unlock()
	store, ok = storeFishDistributeWaterLayer.Load(fitKey)
	if ok {
		tblFishDistributeWaterLayer, ok := store.(*FishDistributeWaterLayer)
		if ok {
			return tblFishDistributeWaterLayer
		}
	}
	tblFishDistributeWaterLayer := make(map[int64]*FishDistributeWaterLayer)
	fish_distribute_water_layer_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeWaterLayer, option...)
	if fish_distribute_water_layer_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_water_layer_str), &tblFishDistributeWaterLayer)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_water_layer", errUnmarshal)
		return nil
	}
	storeFishDistributeWaterLayer.Store(fitKey, tblFishDistributeWaterLayer)
	return tblFishDistributeWaterLayer[id]
}

func LoadAllFishDistributeWaterLayerCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDistributeWaterLayer, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeWaterLayer", successChannels)
	return nil
}

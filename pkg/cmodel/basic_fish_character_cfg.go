// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishCharacter struct {
	Id                int64   `json:"id"`
	Name              string  `json:"name"`
	Description       string  `json:"description"`
	ActivityLevel     int32   `json:"activityLevel"`
	ActivityMax       int32   `json:"activityMax"`
	SpeedFactor       float32 `json:"speedFactor"`
	Appetite          string  `json:"appetite"`
	ColorPreference   string  `json:"colorPreference"`
	BehaviorPattern   string  `json:"behaviorPattern"`
	AdditionalInfo    string  `json:"additionalInfo"`
	HooksetCommFactor float32 `json:"hooksetCommFactor"`
}

var lockBasicFishCharacter sync.RWMutex
var storeBasicFishCharacter sync.Map
var strBasicFishCharacter string = "basic_fish_character"

func InitBasicFishCharacterCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishCharacter, watchBasicFishCharacterFunc)
	return LoadAllBasicFishCharacterCfg()
}

func fixKeyBasicFishCharacter(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishCharacter)
}
func watchBasicFishCharacterFunc(key string, js string) {
	mapBasicFishCharacter := make(map[int64]*BasicFishCharacter)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishCharacter)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishCharacter.Store(key, mapBasicFishCharacter)
}

func GetAllBasicFishCharacter(option ...consulconfig.Option) map[int64]*BasicFishCharacter {
	fitKey := fixKeyBasicFishCharacter(option...)
	store, ok := storeBasicFishCharacter.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishCharacter)
		if ok {
			return storeMap
		}
	}
	lockBasicFishCharacter.Lock()
	defer lockBasicFishCharacter.Unlock()
	store, ok = storeBasicFishCharacter.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishCharacter)
		if ok {
			return storeMap
		}
	}
	tblBasicFishCharacter := make(map[int64]*BasicFishCharacter)
	basic_fish_character_str, err := consulconfig.GetInstance().GetConfig(strBasicFishCharacter, option...)
	if basic_fish_character_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_character_str), &tblBasicFishCharacter)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_character", errUnmarshal)
		return nil
	}
	storeBasicFishCharacter.Store(fitKey, tblBasicFishCharacter)
	return tblBasicFishCharacter
}

func GetBasicFishCharacter(id int64, option ...consulconfig.Option) *BasicFishCharacter {
	fitKey := fixKeyBasicFishCharacter(option...)
	store, ok := storeBasicFishCharacter.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishCharacter)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishCharacter.Lock()
	defer lockBasicFishCharacter.Unlock()
	store, ok = storeBasicFishCharacter.Load(fitKey)
	if ok {
		tblBasicFishCharacter, ok := store.(*BasicFishCharacter)
		if ok {
			return tblBasicFishCharacter
		}
	}
	tblBasicFishCharacter := make(map[int64]*BasicFishCharacter)
	basic_fish_character_str, err := consulconfig.GetInstance().GetConfig(strBasicFishCharacter, option...)
	if basic_fish_character_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_character_str), &tblBasicFishCharacter)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_character", errUnmarshal)
		return nil
	}
	storeBasicFishCharacter.Store(fitKey, tblBasicFishCharacter)
	return tblBasicFishCharacter[id]
}

func LoadAllBasicFishCharacterCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishCharacter, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishCharacter", successChannels)
	return nil
}

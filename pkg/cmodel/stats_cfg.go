// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StatsConds struct {
	Label   int32 `json:"label"`
	Operate int32 `json:"operate"`
	Value   int64 `json:"value"`
}

type Stats struct {
	Id       int64        `json:"id"`
	Name     string       `json:"name"`
	Language int64        `json:"language"`
	Mark     string       `json:"mark"`
	Type     int32        `json:"type"`
	SubType  int32        `json:"subType"`
	Target   int64        `json:"target"`
	Update   int32        `json:"update"`
	Event    int64        `json:"event"`
	Field    int64        `json:"field"`
	Conds    []StatsConds `json:"conds"`
}

var lockStats sync.RWMutex
var storeStats sync.Map
var strStats string = "stats"

func InitStatsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStats, watchStatsFunc)
	return LoadAllStatsCfg()
}

func fixKeyStats(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStats)
}
func watchStatsFunc(key string, js string) {
	mapStats := make(map[int64]*Stats)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStats)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStats.Store(key, mapStats)
}

func GetAllStats(option ...consulconfig.Option) map[int64]*Stats {
	fitKey := fixKeyStats(option...)
	store, ok := storeStats.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Stats)
		if ok {
			return storeMap
		}
	}
	lockStats.Lock()
	defer lockStats.Unlock()
	store, ok = storeStats.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Stats)
		if ok {
			return storeMap
		}
	}
	tblStats := make(map[int64]*Stats)
	stats_str, err := consulconfig.GetInstance().GetConfig(strStats, option...)
	if stats_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_str), &tblStats)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats", errUnmarshal)
		return nil
	}
	storeStats.Store(fitKey, tblStats)
	return tblStats
}

func GetStats(id int64, option ...consulconfig.Option) *Stats {
	fitKey := fixKeyStats(option...)
	store, ok := storeStats.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Stats)
		if ok {
			return storeMap[id]
		}
	}
	lockStats.Lock()
	defer lockStats.Unlock()
	store, ok = storeStats.Load(fitKey)
	if ok {
		tblStats, ok := store.(*Stats)
		if ok {
			return tblStats
		}
	}
	tblStats := make(map[int64]*Stats)
	stats_str, err := consulconfig.GetInstance().GetConfig(strStats, option...)
	if stats_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_str), &tblStats)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats", errUnmarshal)
		return nil
	}
	storeStats.Store(fitKey, tblStats)
	return tblStats[id]
}

func LoadAllStatsCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStats, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Stats", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PoseBasic struct {
	Id             int64   `json:"id"`
	PoseScoreRange []int32 `json:"poseScoreRange"`
}

var lockPoseBasic sync.RWMutex
var storePoseBasic sync.Map
var strPoseBasic string = "pose_basic"

func InitPoseBasicCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPoseBasic, watchPoseBasicFunc)
	return LoadAllPoseBasicCfg()
}

func fixKeyPoseBasic(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPoseBasic)
}
func watchPoseBasicFunc(key string, js string) {
	store, ok := storePoseBasic.Load(key)
	if !ok {
		store = &PoseBasic{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePoseBasic.Store(key, store)
}

func GetPoseBasic(option ...consulconfig.Option) *PoseBasic {
	fitKey := fixKeyPoseBasic(option...)
	store, ok := storePoseBasic.Load(fitKey)
	if ok {
		tblPoseBasic, ok := store.(*PoseBasic)
		if ok {
			return tblPoseBasic
		}
	}
	lockPoseBasic.Lock()
	defer lockPoseBasic.Unlock()
	store, ok = storePoseBasic.Load(fitKey)
	if ok {
		tblPoseBasic, ok := store.(*PoseBasic)
		if ok {
			return tblPoseBasic
		}
	}
	tblPoseBasic := &PoseBasic{}
	pose_basic_str, err := consulconfig.GetInstance().GetConfig(strPoseBasic, option...)
	if pose_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_basic_str), &tblPoseBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strPoseBasic, errUnmarshal, pose_basic_str)
		return nil
	}
	storePoseBasic.Store(fitKey, tblPoseBasic)
	return tblPoseBasic
}

func LoadAllPoseBasicCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPoseBasic, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PoseBasic", successChannels)
	return nil
}

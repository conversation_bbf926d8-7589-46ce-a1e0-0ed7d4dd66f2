// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeTemperatureTemperature struct {
	TemperatureId int64 `json:"temperatureId"`
	Weight        int32 `json:"weight"`
}

type FishDistributeTemperature struct {
	Id          int64                                  `json:"id"`
	Name        string                                 `json:"name"`
	Temperature []FishDistributeTemperatureTemperature `json:"temperature"`
}

var lockFishDistributeTemperature sync.RWMutex
var storeFishDistributeTemperature sync.Map
var strFishDistributeTemperature string = "fish_distribute_temperature"

func InitFishDistributeTemperatureCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeTemperature, watchFishDistributeTemperatureFunc)
	return LoadAllFishDistributeTemperatureCfg()
}

func fixKeyFishDistributeTemperature(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeTemperature)
}
func watchFishDistributeTemperatureFunc(key string, js string) {
	mapFishDistributeTemperature := make(map[int64]*FishDistributeTemperature)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributeTemperature)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeTemperature.Store(key, mapFishDistributeTemperature)
}

func GetAllFishDistributeTemperature(option ...consulconfig.Option) map[int64]*FishDistributeTemperature {
	fitKey := fixKeyFishDistributeTemperature(option...)
	store, ok := storeFishDistributeTemperature.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeTemperature)
		if ok {
			return storeMap
		}
	}
	lockFishDistributeTemperature.Lock()
	defer lockFishDistributeTemperature.Unlock()
	store, ok = storeFishDistributeTemperature.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeTemperature)
		if ok {
			return storeMap
		}
	}
	tblFishDistributeTemperature := make(map[int64]*FishDistributeTemperature)
	fish_distribute_temperature_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeTemperature, option...)
	if fish_distribute_temperature_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_temperature_str), &tblFishDistributeTemperature)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_temperature", errUnmarshal)
		return nil
	}
	storeFishDistributeTemperature.Store(fitKey, tblFishDistributeTemperature)
	return tblFishDistributeTemperature
}

func GetFishDistributeTemperature(id int64, option ...consulconfig.Option) *FishDistributeTemperature {
	fitKey := fixKeyFishDistributeTemperature(option...)
	store, ok := storeFishDistributeTemperature.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeTemperature)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributeTemperature.Lock()
	defer lockFishDistributeTemperature.Unlock()
	store, ok = storeFishDistributeTemperature.Load(fitKey)
	if ok {
		tblFishDistributeTemperature, ok := store.(*FishDistributeTemperature)
		if ok {
			return tblFishDistributeTemperature
		}
	}
	tblFishDistributeTemperature := make(map[int64]*FishDistributeTemperature)
	fish_distribute_temperature_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeTemperature, option...)
	if fish_distribute_temperature_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_temperature_str), &tblFishDistributeTemperature)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_temperature", errUnmarshal)
		return nil
	}
	storeFishDistributeTemperature.Store(fitKey, tblFishDistributeTemperature)
	return tblFishDistributeTemperature[id]
}

func LoadAllFishDistributeTemperatureCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDistributeTemperature, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeTemperature", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type NoviceSceneGuide struct {
	Id     int64   `json:"id"`
	Mode   int32   `json:"mode"`
	Mark   string  `json:"mark"`
	Type   int32   `json:"type"`
	Args   []int64 `json:"args"`
	GiftId int64   `json:"giftId"`
}

var lockNoviceSceneGuide sync.RWMutex
var storeNoviceSceneGuide sync.Map
var strNoviceSceneGuide string = "novice_scene_guide"

func InitNoviceSceneGuideCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strNoviceSceneGuide, watchNoviceSceneGuideFunc)
	return LoadAllNoviceSceneGuideCfg()
}

func fixKeyNoviceSceneGuide(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strNoviceSceneGuide)
}
func watchNoviceSceneGuideFunc(key string, js string) {
	mapNoviceSceneGuide := make(map[int64]*NoviceSceneGuide)
	errUnmarshal := json.Unmarshal([]byte(js), &mapNoviceSceneGuide)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeNoviceSceneGuide.Store(key, mapNoviceSceneGuide)
}

func GetAllNoviceSceneGuide(option ...consulconfig.Option) map[int64]*NoviceSceneGuide {
	fitKey := fixKeyNoviceSceneGuide(option...)
	store, ok := storeNoviceSceneGuide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneGuide)
		if ok {
			return storeMap
		}
	}
	lockNoviceSceneGuide.Lock()
	defer lockNoviceSceneGuide.Unlock()
	store, ok = storeNoviceSceneGuide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneGuide)
		if ok {
			return storeMap
		}
	}
	tblNoviceSceneGuide := make(map[int64]*NoviceSceneGuide)
	novice_scene_guide_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneGuide, option...)
	if novice_scene_guide_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_guide_str), &tblNoviceSceneGuide)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_guide", errUnmarshal)
		return nil
	}
	storeNoviceSceneGuide.Store(fitKey, tblNoviceSceneGuide)
	return tblNoviceSceneGuide
}

func GetNoviceSceneGuide(id int64, option ...consulconfig.Option) *NoviceSceneGuide {
	fitKey := fixKeyNoviceSceneGuide(option...)
	store, ok := storeNoviceSceneGuide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneGuide)
		if ok {
			return storeMap[id]
		}
	}
	lockNoviceSceneGuide.Lock()
	defer lockNoviceSceneGuide.Unlock()
	store, ok = storeNoviceSceneGuide.Load(fitKey)
	if ok {
		tblNoviceSceneGuide, ok := store.(*NoviceSceneGuide)
		if ok {
			return tblNoviceSceneGuide
		}
	}
	tblNoviceSceneGuide := make(map[int64]*NoviceSceneGuide)
	novice_scene_guide_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneGuide, option...)
	if novice_scene_guide_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_guide_str), &tblNoviceSceneGuide)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_guide", errUnmarshal)
		return nil
	}
	storeNoviceSceneGuide.Store(fitKey, tblNoviceSceneGuide)
	return tblNoviceSceneGuide[id]
}

func LoadAllNoviceSceneGuideCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strNoviceSceneGuide, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "NoviceSceneGuide", successChannels)
	return nil
}

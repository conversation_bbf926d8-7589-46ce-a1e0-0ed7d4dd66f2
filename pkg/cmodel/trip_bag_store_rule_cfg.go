// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TripBagStoreRule struct {
	Id       int64 `json:"id"`
	BagType  int32 `json:"bagType"`
	ItemType int32 `json:"itemType"`
}

var lockTripBagStoreRule sync.RWMutex
var storeTripBagStoreRule sync.Map
var strTripBagStoreRule string = "trip_bag_store_rule"

func InitTripBagStoreRuleCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTripBagStoreRule, watchTripBagStoreRuleFunc)
	return LoadAllTripBagStoreRuleCfg()
}

func fixKeyTripBagStoreRule(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTripBagStoreRule)
}
func watchTripBagStoreRuleFunc(key string, js string) {
	mapTripBagStoreRule := make(map[int64]*TripBagStoreRule)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTripBagStoreRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTripBagStoreRule.Store(key, mapTripBagStoreRule)
}

func GetAllTripBagStoreRule(option ...consulconfig.Option) map[int64]*TripBagStoreRule {
	fitKey := fixKeyTripBagStoreRule(option...)
	store, ok := storeTripBagStoreRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TripBagStoreRule)
		if ok {
			return storeMap
		}
	}
	lockTripBagStoreRule.Lock()
	defer lockTripBagStoreRule.Unlock()
	store, ok = storeTripBagStoreRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TripBagStoreRule)
		if ok {
			return storeMap
		}
	}
	tblTripBagStoreRule := make(map[int64]*TripBagStoreRule)
	trip_bag_store_rule_str, err := consulconfig.GetInstance().GetConfig(strTripBagStoreRule, option...)
	if trip_bag_store_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(trip_bag_store_rule_str), &tblTripBagStoreRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "trip_bag_store_rule", errUnmarshal)
		return nil
	}
	storeTripBagStoreRule.Store(fitKey, tblTripBagStoreRule)
	return tblTripBagStoreRule
}

func GetTripBagStoreRule(id int64, option ...consulconfig.Option) *TripBagStoreRule {
	fitKey := fixKeyTripBagStoreRule(option...)
	store, ok := storeTripBagStoreRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TripBagStoreRule)
		if ok {
			return storeMap[id]
		}
	}
	lockTripBagStoreRule.Lock()
	defer lockTripBagStoreRule.Unlock()
	store, ok = storeTripBagStoreRule.Load(fitKey)
	if ok {
		tblTripBagStoreRule, ok := store.(*TripBagStoreRule)
		if ok {
			return tblTripBagStoreRule
		}
	}
	tblTripBagStoreRule := make(map[int64]*TripBagStoreRule)
	trip_bag_store_rule_str, err := consulconfig.GetInstance().GetConfig(strTripBagStoreRule, option...)
	if trip_bag_store_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(trip_bag_store_rule_str), &tblTripBagStoreRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "trip_bag_store_rule", errUnmarshal)
		return nil
	}
	storeTripBagStoreRule.Store(fitKey, tblTripBagStoreRule)
	return tblTripBagStoreRule[id]
}

func LoadAllTripBagStoreRuleCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTripBagStoreRule, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TripBagStoreRule", successChannels)
	return nil
}

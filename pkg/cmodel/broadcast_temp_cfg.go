// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BroadcastTemp struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	Content int64  `json:"content"`
	Remark  string `json:"remark"`
}

var lockBroadcastTemp sync.RWMutex
var storeBroadcastTemp sync.Map
var strBroadcastTemp string = "broadcast_temp"

func InitBroadcastTempCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBroadcastTemp, watchBroadcastTempFunc)
	return LoadAllBroadcastTempCfg()
}

func fixKeyBroadcastTemp(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBroadcastTemp)
}
func watchBroadcastTempFunc(key string, js string) {
	mapBroadcastTemp := make(map[int64]*BroadcastTemp)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBroadcastTemp)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBroadcastTemp.Store(key, mapBroadcastTemp)
}

func GetAllBroadcastTemp(option ...consulconfig.Option) map[int64]*BroadcastTemp {
	fitKey := fixKeyBroadcastTemp(option...)
	store, ok := storeBroadcastTemp.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BroadcastTemp)
		if ok {
			return storeMap
		}
	}
	lockBroadcastTemp.Lock()
	defer lockBroadcastTemp.Unlock()
	store, ok = storeBroadcastTemp.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BroadcastTemp)
		if ok {
			return storeMap
		}
	}
	tblBroadcastTemp := make(map[int64]*BroadcastTemp)
	broadcast_temp_str, err := consulconfig.GetInstance().GetConfig(strBroadcastTemp, option...)
	if broadcast_temp_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(broadcast_temp_str), &tblBroadcastTemp)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "broadcast_temp", errUnmarshal)
		return nil
	}
	storeBroadcastTemp.Store(fitKey, tblBroadcastTemp)
	return tblBroadcastTemp
}

func GetBroadcastTemp(id int64, option ...consulconfig.Option) *BroadcastTemp {
	fitKey := fixKeyBroadcastTemp(option...)
	store, ok := storeBroadcastTemp.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BroadcastTemp)
		if ok {
			return storeMap[id]
		}
	}
	lockBroadcastTemp.Lock()
	defer lockBroadcastTemp.Unlock()
	store, ok = storeBroadcastTemp.Load(fitKey)
	if ok {
		tblBroadcastTemp, ok := store.(*BroadcastTemp)
		if ok {
			return tblBroadcastTemp
		}
	}
	tblBroadcastTemp := make(map[int64]*BroadcastTemp)
	broadcast_temp_str, err := consulconfig.GetInstance().GetConfig(strBroadcastTemp, option...)
	if broadcast_temp_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(broadcast_temp_str), &tblBroadcastTemp)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "broadcast_temp", errUnmarshal)
		return nil
	}
	storeBroadcastTemp.Store(fitKey, tblBroadcastTemp)
	return tblBroadcastTemp[id]
}

func LoadAllBroadcastTempCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBroadcastTemp, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BroadcastTemp", successChannels)
	return nil
}

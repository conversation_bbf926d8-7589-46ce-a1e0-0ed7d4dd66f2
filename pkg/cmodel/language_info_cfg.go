// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageInfo struct {
	Id                int64  `json:"id"`
	Name              string `json:"name"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
	Mark              string `json:"mark"`
}

var lockLanguageInfo sync.RWMutex
var storeLanguageInfo sync.Map
var strLanguageInfo string = "language_info"

func InitLanguageInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageInfo, watchLanguageInfoFunc)
	return LoadAllLanguageInfoCfg()
}

func fixKeyLanguageInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageInfo)
}
func watchLanguageInfoFunc(key string, js string) {
	mapLanguageInfo := make(map[int64]*LanguageInfo)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageInfo.Store(key, mapLanguageInfo)
}

func GetAllLanguageInfo(option ...consulconfig.Option) map[int64]*LanguageInfo {
	fitKey := fixKeyLanguageInfo(option...)
	store, ok := storeLanguageInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageInfo)
		if ok {
			return storeMap
		}
	}
	lockLanguageInfo.Lock()
	defer lockLanguageInfo.Unlock()
	store, ok = storeLanguageInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageInfo)
		if ok {
			return storeMap
		}
	}
	tblLanguageInfo := make(map[int64]*LanguageInfo)
	language_info_str, err := consulconfig.GetInstance().GetConfig(strLanguageInfo, option...)
	if language_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_info_str), &tblLanguageInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_info", errUnmarshal)
		return nil
	}
	storeLanguageInfo.Store(fitKey, tblLanguageInfo)
	return tblLanguageInfo
}

func GetLanguageInfo(id int64, option ...consulconfig.Option) *LanguageInfo {
	fitKey := fixKeyLanguageInfo(option...)
	store, ok := storeLanguageInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageInfo)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageInfo.Lock()
	defer lockLanguageInfo.Unlock()
	store, ok = storeLanguageInfo.Load(fitKey)
	if ok {
		tblLanguageInfo, ok := store.(*LanguageInfo)
		if ok {
			return tblLanguageInfo
		}
	}
	tblLanguageInfo := make(map[int64]*LanguageInfo)
	language_info_str, err := consulconfig.GetInstance().GetConfig(strLanguageInfo, option...)
	if language_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_info_str), &tblLanguageInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_info", errUnmarshal)
		return nil
	}
	storeLanguageInfo.Store(fitKey, tblLanguageInfo)
	return tblLanguageInfo[id]
}

func LoadAllLanguageInfoCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageInfo", successChannels)
	return nil
}

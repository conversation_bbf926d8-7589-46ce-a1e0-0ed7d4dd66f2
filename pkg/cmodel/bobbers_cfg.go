// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Bobbers struct {
	Id                int64   `json:"id"`
	Brand             int32   `json:"brand"`
	Series            int32   `json:"series"`
	SizeName          string  `json:"sizeName"`
	SeriesDes         string  `json:"seriesDes"`
	SubType           int32   `json:"subType"`
	Name              string  `json:"name"`
	Mark              string  `json:"mark"`
	ArtId             string  `json:"artId"`
	Durability        int32   `json:"durability"`
	Weight            int32   `json:"weight"`
	BuoyancyFactor    float32 `json:"buoyancyFactor"`
	BuoyancyHold      float32 `json:"buoyancyHold"`
	WindageFactor     float32 `json:"windageFactor"`
	SensitivityFactor float32 `json:"sensitivityFactor"`
	SplashType        int32   `json:"splashType"`
}

var lockBobbers sync.RWMutex
var storeBobbers sync.Map
var strBobbers string = "bobbers"

func InitBobbersCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBobbers, watchBobbersFunc)
	return LoadAllBobbersCfg()
}

func fixKeyBobbers(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBobbers)
}
func watchBobbersFunc(key string, js string) {
	mapBobbers := make(map[int64]*Bobbers)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBobbers)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBobbers.Store(key, mapBobbers)
}

func GetAllBobbers(option ...consulconfig.Option) map[int64]*Bobbers {
	fitKey := fixKeyBobbers(option...)
	store, ok := storeBobbers.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Bobbers)
		if ok {
			return storeMap
		}
	}
	lockBobbers.Lock()
	defer lockBobbers.Unlock()
	store, ok = storeBobbers.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Bobbers)
		if ok {
			return storeMap
		}
	}
	tblBobbers := make(map[int64]*Bobbers)
	bobbers_str, err := consulconfig.GetInstance().GetConfig(strBobbers, option...)
	if bobbers_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bobbers_str), &tblBobbers)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bobbers", errUnmarshal)
		return nil
	}
	storeBobbers.Store(fitKey, tblBobbers)
	return tblBobbers
}

func GetBobbers(id int64, option ...consulconfig.Option) *Bobbers {
	fitKey := fixKeyBobbers(option...)
	store, ok := storeBobbers.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Bobbers)
		if ok {
			return storeMap[id]
		}
	}
	lockBobbers.Lock()
	defer lockBobbers.Unlock()
	store, ok = storeBobbers.Load(fitKey)
	if ok {
		tblBobbers, ok := store.(*Bobbers)
		if ok {
			return tblBobbers
		}
	}
	tblBobbers := make(map[int64]*Bobbers)
	bobbers_str, err := consulconfig.GetInstance().GetConfig(strBobbers, option...)
	if bobbers_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bobbers_str), &tblBobbers)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bobbers", errUnmarshal)
		return nil
	}
	storeBobbers.Store(fitKey, tblBobbers)
	return tblBobbers[id]
}

func LoadAllBobbersCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBobbers, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Bobbers", successChannels)
	return nil
}

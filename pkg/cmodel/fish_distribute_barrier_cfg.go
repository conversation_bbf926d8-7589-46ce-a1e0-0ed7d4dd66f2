// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeBarrierBarrier struct {
	BarrierId int64 `json:"barrierId"`
	Weight    int32 `json:"weight"`
}

type FishDistributeBarrier struct {
	Id      int64                          `json:"id"`
	Name    string                         `json:"name"`
	Barrier []FishDistributeBarrierBarrier `json:"barrier"`
}

var lockFishDistributeBarrier sync.RWMutex
var storeFishDistributeBarrier sync.Map
var strFishDistributeBarrier string = "fish_distribute_barrier"

func InitFishDistributeBarrierCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeBarrier, watchFishDistributeBarrierFunc)
	return LoadAllFishDistributeBarrierCfg()
}

func fixKeyFishDistributeBarrier(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeBarrier)
}
func watchFishDistributeBarrierFunc(key string, js string) {
	mapFishDistributeBarrier := make(map[int64]*FishDistributeBarrier)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributeBarrier)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeBarrier.Store(key, mapFishDistributeBarrier)
}

func GetAllFishDistributeBarrier(option ...consulconfig.Option) map[int64]*FishDistributeBarrier {
	fitKey := fixKeyFishDistributeBarrier(option...)
	store, ok := storeFishDistributeBarrier.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeBarrier)
		if ok {
			return storeMap
		}
	}
	lockFishDistributeBarrier.Lock()
	defer lockFishDistributeBarrier.Unlock()
	store, ok = storeFishDistributeBarrier.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeBarrier)
		if ok {
			return storeMap
		}
	}
	tblFishDistributeBarrier := make(map[int64]*FishDistributeBarrier)
	fish_distribute_barrier_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeBarrier, option...)
	if fish_distribute_barrier_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_barrier_str), &tblFishDistributeBarrier)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_barrier", errUnmarshal)
		return nil
	}
	storeFishDistributeBarrier.Store(fitKey, tblFishDistributeBarrier)
	return tblFishDistributeBarrier
}

func GetFishDistributeBarrier(id int64, option ...consulconfig.Option) *FishDistributeBarrier {
	fitKey := fixKeyFishDistributeBarrier(option...)
	store, ok := storeFishDistributeBarrier.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeBarrier)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributeBarrier.Lock()
	defer lockFishDistributeBarrier.Unlock()
	store, ok = storeFishDistributeBarrier.Load(fitKey)
	if ok {
		tblFishDistributeBarrier, ok := store.(*FishDistributeBarrier)
		if ok {
			return tblFishDistributeBarrier
		}
	}
	tblFishDistributeBarrier := make(map[int64]*FishDistributeBarrier)
	fish_distribute_barrier_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeBarrier, option...)
	if fish_distribute_barrier_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_barrier_str), &tblFishDistributeBarrier)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_barrier", errUnmarshal)
		return nil
	}
	storeFishDistributeBarrier.Store(fitKey, tblFishDistributeBarrier)
	return tblFishDistributeBarrier[id]
}

func LoadAllFishDistributeBarrierCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDistributeBarrier, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeBarrier", successChannels)
	return nil
}

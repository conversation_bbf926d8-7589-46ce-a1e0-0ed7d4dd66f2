// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishStock struct {
	Id                int64   `json:"id"`
	Name              string  `json:"name"`
	ResetDayTime      string  `json:"resetDayTime"`
	NoneFishWeight    int32   `json:"noneFishWeight"`
	MaxTimeMultiplier float64 `json:"maxTimeMultiplier"`
	TimeExpectFactor  float64 `json:"timeExpectFactor"`
}

var lockFishStock sync.RWMutex
var storeFishStock sync.Map
var strFishStock string = "fish_stock"

func InitFishStockCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishStock, watchFishStockFunc)
	return LoadAllFishStockCfg()
}

func fixKeyFishStock(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishStock)
}
func watchFishStockFunc(key string, js string) {
	mapFishStock := make(map[int64]*FishStock)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishStock)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishStock.Store(key, mapFishStock)
}

func GetAllFishStock(option ...consulconfig.Option) map[int64]*FishStock {
	fitKey := fixKeyFishStock(option...)
	store, ok := storeFishStock.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishStock)
		if ok {
			return storeMap
		}
	}
	lockFishStock.Lock()
	defer lockFishStock.Unlock()
	store, ok = storeFishStock.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishStock)
		if ok {
			return storeMap
		}
	}
	tblFishStock := make(map[int64]*FishStock)
	fish_stock_str, err := consulconfig.GetInstance().GetConfig(strFishStock, option...)
	if fish_stock_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_stock_str), &tblFishStock)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_stock", errUnmarshal)
		return nil
	}
	storeFishStock.Store(fitKey, tblFishStock)
	return tblFishStock
}

func GetFishStock(id int64, option ...consulconfig.Option) *FishStock {
	fitKey := fixKeyFishStock(option...)
	store, ok := storeFishStock.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishStock)
		if ok {
			return storeMap[id]
		}
	}
	lockFishStock.Lock()
	defer lockFishStock.Unlock()
	store, ok = storeFishStock.Load(fitKey)
	if ok {
		tblFishStock, ok := store.(*FishStock)
		if ok {
			return tblFishStock
		}
	}
	tblFishStock := make(map[int64]*FishStock)
	fish_stock_str, err := consulconfig.GetInstance().GetConfig(strFishStock, option...)
	if fish_stock_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_stock_str), &tblFishStock)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_stock", errUnmarshal)
		return nil
	}
	storeFishStock.Store(fitKey, tblFishStock)
	return tblFishStock[id]
}

func LoadAllFishStockCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishStock, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishStock", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Global struct {
	InitGold      int64 `json:"initGold"`
	InitDiamond   int64 `json:"initDiamond"`
	RegAwardGuest int64 `json:"regAwardGuest"`
	RegAward3rd   int64 `json:"regAward3rd"`
	GameNatureDay int64 `json:"gameNatureDay"`
}

var lockGlobal sync.RWMutex
var storeGlobal sync.Map
var strGlobal string = "global"

func InitGlobalCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strGlobal, watchGlobalFunc)
	return LoadAllGlobalCfg()
}

func fixKeyGlobal(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strGlobal)
}
func watchGlobalFunc(key string, js string) {
	store, ok := storeGlobal.Load(key)
	if !ok {
		store = &Global{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeGlobal.Store(key, store)
}

func GetGlobal(option ...consulconfig.Option) *Global {
	fitKey := fixKeyGlobal(option...)
	store, ok := storeGlobal.Load(fitKey)
	if ok {
		tblGlobal, ok := store.(*Global)
		if ok {
			return tblGlobal
		}
	}
	lockGlobal.Lock()
	defer lockGlobal.Unlock()
	store, ok = storeGlobal.Load(fitKey)
	if ok {
		tblGlobal, ok := store.(*Global)
		if ok {
			return tblGlobal
		}
	}
	tblGlobal := &Global{}
	global_str, err := consulconfig.GetInstance().GetConfig(strGlobal, option...)
	if global_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(global_str), &tblGlobal)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strGlobal, errUnmarshal, global_str)
		return nil
	}
	storeGlobal.Store(fitKey, tblGlobal)
	return tblGlobal
}

func LoadAllGlobalCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strGlobal, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Global", successChannels)
	return nil
}

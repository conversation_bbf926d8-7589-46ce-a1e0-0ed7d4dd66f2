// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type GiftBasicItem struct {
	ItemId int64 `json:"itemId"`
	Count  int64 `json:"count"`
}

type GiftBasic struct {
	Id         int64           `json:"id"`
	Name       string          `json:"name"`
	Itemtypeid int64           `json:"itemtypeid"`
	ShowImage  string          `json:"showImage"`
	Item       []GiftBasicItem `json:"item"`
}

var lockGiftBasic sync.RWMutex
var storeGiftBasic sync.Map
var strGiftBasic string = "gift_basic"

func InitGiftBasicCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strGiftBasic, watchGiftBasicFunc)
	return LoadAllGiftBasicCfg()
}

func fixKeyGiftBasic(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strGiftBasic)
}
func watchGiftBasicFunc(key string, js string) {
	mapGiftBasic := make(map[int64]*GiftBasic)
	errUnmarshal := json.Unmarshal([]byte(js), &mapGiftBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeGiftBasic.Store(key, mapGiftBasic)
}

func GetAllGiftBasic(option ...consulconfig.Option) map[int64]*GiftBasic {
	fitKey := fixKeyGiftBasic(option...)
	store, ok := storeGiftBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GiftBasic)
		if ok {
			return storeMap
		}
	}
	lockGiftBasic.Lock()
	defer lockGiftBasic.Unlock()
	store, ok = storeGiftBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GiftBasic)
		if ok {
			return storeMap
		}
	}
	tblGiftBasic := make(map[int64]*GiftBasic)
	gift_basic_str, err := consulconfig.GetInstance().GetConfig(strGiftBasic, option...)
	if gift_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(gift_basic_str), &tblGiftBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "gift_basic", errUnmarshal)
		return nil
	}
	storeGiftBasic.Store(fitKey, tblGiftBasic)
	return tblGiftBasic
}

func GetGiftBasic(id int64, option ...consulconfig.Option) *GiftBasic {
	fitKey := fixKeyGiftBasic(option...)
	store, ok := storeGiftBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GiftBasic)
		if ok {
			return storeMap[id]
		}
	}
	lockGiftBasic.Lock()
	defer lockGiftBasic.Unlock()
	store, ok = storeGiftBasic.Load(fitKey)
	if ok {
		tblGiftBasic, ok := store.(*GiftBasic)
		if ok {
			return tblGiftBasic
		}
	}
	tblGiftBasic := make(map[int64]*GiftBasic)
	gift_basic_str, err := consulconfig.GetInstance().GetConfig(strGiftBasic, option...)
	if gift_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(gift_basic_str), &tblGiftBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "gift_basic", errUnmarshal)
		return nil
	}
	storeGiftBasic.Store(fitKey, tblGiftBasic)
	return tblGiftBasic[id]
}

func LoadAllGiftBasicCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strGiftBasic, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "GiftBasic", successChannels)
	return nil
}

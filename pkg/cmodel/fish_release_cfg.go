// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishRelease struct {
	Id              int64   `json:"id"`
	StockId         int64   `json:"stockId"`
	FishId          int64   `json:"fishId"`
	FishNum         int32   `json:"fishNum"`
	ProbWeightIdeal int32   `json:"probWeightIdeal"`
	MinEnvCoeff     float64 `json:"minEnvCoeff"`
	MinAdaptCoeff   float64 `json:"minAdaptCoeff"`
	LengthMin       int32   `json:"lengthMin"`
	LengthMax       int32   `json:"lengthMax"`
	IsReduce        bool    `json:"isReduce"`
}

var lockFishRelease sync.RWMutex
var storeFishRelease sync.Map
var strFishRelease string = "fish_release"

func InitFishReleaseCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishRelease, watchFishReleaseFunc)
	return LoadAllFishReleaseCfg()
}

func fixKeyFishRelease(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishRelease)
}
func watchFishReleaseFunc(key string, js string) {
	mapFishRelease := make(map[int64]*FishRelease)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishRelease)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishRelease.Store(key, mapFishRelease)
}

func GetAllFishRelease(option ...consulconfig.Option) map[int64]*FishRelease {
	fitKey := fixKeyFishRelease(option...)
	store, ok := storeFishRelease.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishRelease)
		if ok {
			return storeMap
		}
	}
	lockFishRelease.Lock()
	defer lockFishRelease.Unlock()
	store, ok = storeFishRelease.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishRelease)
		if ok {
			return storeMap
		}
	}
	tblFishRelease := make(map[int64]*FishRelease)
	fish_release_str, err := consulconfig.GetInstance().GetConfig(strFishRelease, option...)
	if fish_release_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_release_str), &tblFishRelease)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_release", errUnmarshal)
		return nil
	}
	storeFishRelease.Store(fitKey, tblFishRelease)
	return tblFishRelease
}

func GetFishRelease(id int64, option ...consulconfig.Option) *FishRelease {
	fitKey := fixKeyFishRelease(option...)
	store, ok := storeFishRelease.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishRelease)
		if ok {
			return storeMap[id]
		}
	}
	lockFishRelease.Lock()
	defer lockFishRelease.Unlock()
	store, ok = storeFishRelease.Load(fitKey)
	if ok {
		tblFishRelease, ok := store.(*FishRelease)
		if ok {
			return tblFishRelease
		}
	}
	tblFishRelease := make(map[int64]*FishRelease)
	fish_release_str, err := consulconfig.GetInstance().GetConfig(strFishRelease, option...)
	if fish_release_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_release_str), &tblFishRelease)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_release", errUnmarshal)
		return nil
	}
	storeFishRelease.Store(fitKey, tblFishRelease)
	return tblFishRelease[id]
}

func LoadAllFishReleaseCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishRelease, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishRelease", successChannels)
	return nil
}

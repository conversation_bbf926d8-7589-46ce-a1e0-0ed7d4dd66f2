// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ItemFood struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	Mark      string `json:"mark"`
	ArtId     string `json:"artId"`
	SaleType  int32  `json:"saleType"`
	Weight    int32  `json:"weight"`
	ItemType  int32  `json:"itemType"`
	AddEnergy int32  `json:"addEnergy"`
}

var lockItemFood sync.RWMutex
var storeItemFood sync.Map
var strItemFood string = "item_food"

func InitItemFoodCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItemFood, watchItemFoodFunc)
	return LoadAllItemFoodCfg()
}

func fixKeyItemFood(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItemFood)
}
func watchItemFoodFunc(key string, js string) {
	mapItemFood := make(map[int64]*ItemFood)
	errUnmarshal := json.Unmarshal([]byte(js), &mapItemFood)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItemFood.Store(key, mapItemFood)
}

func GetAllItemFood(option ...consulconfig.Option) map[int64]*ItemFood {
	fitKey := fixKeyItemFood(option...)
	store, ok := storeItemFood.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemFood)
		if ok {
			return storeMap
		}
	}
	lockItemFood.Lock()
	defer lockItemFood.Unlock()
	store, ok = storeItemFood.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemFood)
		if ok {
			return storeMap
		}
	}
	tblItemFood := make(map[int64]*ItemFood)
	item_food_str, err := consulconfig.GetInstance().GetConfig(strItemFood, option...)
	if item_food_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_food_str), &tblItemFood)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_food", errUnmarshal)
		return nil
	}
	storeItemFood.Store(fitKey, tblItemFood)
	return tblItemFood
}

func GetItemFood(id int64, option ...consulconfig.Option) *ItemFood {
	fitKey := fixKeyItemFood(option...)
	store, ok := storeItemFood.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemFood)
		if ok {
			return storeMap[id]
		}
	}
	lockItemFood.Lock()
	defer lockItemFood.Unlock()
	store, ok = storeItemFood.Load(fitKey)
	if ok {
		tblItemFood, ok := store.(*ItemFood)
		if ok {
			return tblItemFood
		}
	}
	tblItemFood := make(map[int64]*ItemFood)
	item_food_str, err := consulconfig.GetInstance().GetConfig(strItemFood, option...)
	if item_food_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_food_str), &tblItemFood)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_food", errUnmarshal)
		return nil
	}
	storeItemFood.Store(fitKey, tblItemFood)
	return tblItemFood[id]
}

func LoadAllItemFoodCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItemFood, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ItemFood", successChannels)
	return nil
}

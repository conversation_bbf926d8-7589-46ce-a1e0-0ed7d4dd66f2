// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishPeriodDayTime struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type BasicFishPeriod struct {
	Id               int64                  `json:"id"`
	Name             string                 `json:"name"`
	Description      string                 `json:"description"`
	DayTime          BasicFishPeriodDayTime `json:"dayTime"`
	WeightGainFactor float64                `json:"weightGainFactor"`
}

var lockBasicFishPeriod sync.RWMutex
var storeBasicFishPeriod sync.Map
var strBasicFishPeriod string = "basic_fish_period"

func InitBasicFishPeriodCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishPeriod, watchBasicFishPeriodFunc)
	return LoadAllBasicFishPeriodCfg()
}

func fixKeyBasicFishPeriod(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishPeriod)
}
func watchBasicFishPeriodFunc(key string, js string) {
	mapBasicFishPeriod := make(map[int64]*BasicFishPeriod)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishPeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishPeriod.Store(key, mapBasicFishPeriod)
}

func GetAllBasicFishPeriod(option ...consulconfig.Option) map[int64]*BasicFishPeriod {
	fitKey := fixKeyBasicFishPeriod(option...)
	store, ok := storeBasicFishPeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishPeriod)
		if ok {
			return storeMap
		}
	}
	lockBasicFishPeriod.Lock()
	defer lockBasicFishPeriod.Unlock()
	store, ok = storeBasicFishPeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishPeriod)
		if ok {
			return storeMap
		}
	}
	tblBasicFishPeriod := make(map[int64]*BasicFishPeriod)
	basic_fish_period_str, err := consulconfig.GetInstance().GetConfig(strBasicFishPeriod, option...)
	if basic_fish_period_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_period_str), &tblBasicFishPeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_period", errUnmarshal)
		return nil
	}
	storeBasicFishPeriod.Store(fitKey, tblBasicFishPeriod)
	return tblBasicFishPeriod
}

func GetBasicFishPeriod(id int64, option ...consulconfig.Option) *BasicFishPeriod {
	fitKey := fixKeyBasicFishPeriod(option...)
	store, ok := storeBasicFishPeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishPeriod)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishPeriod.Lock()
	defer lockBasicFishPeriod.Unlock()
	store, ok = storeBasicFishPeriod.Load(fitKey)
	if ok {
		tblBasicFishPeriod, ok := store.(*BasicFishPeriod)
		if ok {
			return tblBasicFishPeriod
		}
	}
	tblBasicFishPeriod := make(map[int64]*BasicFishPeriod)
	basic_fish_period_str, err := consulconfig.GetInstance().GetConfig(strBasicFishPeriod, option...)
	if basic_fish_period_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_period_str), &tblBasicFishPeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_period", errUnmarshal)
		return nil
	}
	storeBasicFishPeriod.Store(fitKey, tblBasicFishPeriod)
	return tblBasicFishPeriod[id]
}

func LoadAllBasicFishPeriodCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishPeriod, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishPeriod", successChannels)
	return nil
}

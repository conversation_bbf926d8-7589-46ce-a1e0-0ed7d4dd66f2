// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguagePondLangName struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguagePondLangDes struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguagePond struct {
	Id       int64                `json:"id"`
	Name     string               `json:"name"`
	LangName LanguagePondLangName `json:"lang_name"`
	LangDes  LanguagePondLangDes  `json:"lang_des"`
}

var lockLanguagePond sync.RWMutex
var storeLanguagePond sync.Map
var strLanguagePond string = "language_pond"

func InitLanguagePondCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguagePond, watchLanguagePondFunc)
	return LoadAllLanguagePondCfg()
}

func fixKeyLanguagePond(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguagePond)
}
func watchLanguagePondFunc(key string, js string) {
	mapLanguagePond := make(map[int64]*LanguagePond)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguagePond)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguagePond.Store(key, mapLanguagePond)
}

func GetAllLanguagePond(option ...consulconfig.Option) map[int64]*LanguagePond {
	fitKey := fixKeyLanguagePond(option...)
	store, ok := storeLanguagePond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguagePond)
		if ok {
			return storeMap
		}
	}
	lockLanguagePond.Lock()
	defer lockLanguagePond.Unlock()
	store, ok = storeLanguagePond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguagePond)
		if ok {
			return storeMap
		}
	}
	tblLanguagePond := make(map[int64]*LanguagePond)
	language_pond_str, err := consulconfig.GetInstance().GetConfig(strLanguagePond, option...)
	if language_pond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_pond_str), &tblLanguagePond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_pond", errUnmarshal)
		return nil
	}
	storeLanguagePond.Store(fitKey, tblLanguagePond)
	return tblLanguagePond
}

func GetLanguagePond(id int64, option ...consulconfig.Option) *LanguagePond {
	fitKey := fixKeyLanguagePond(option...)
	store, ok := storeLanguagePond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguagePond)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguagePond.Lock()
	defer lockLanguagePond.Unlock()
	store, ok = storeLanguagePond.Load(fitKey)
	if ok {
		tblLanguagePond, ok := store.(*LanguagePond)
		if ok {
			return tblLanguagePond
		}
	}
	tblLanguagePond := make(map[int64]*LanguagePond)
	language_pond_str, err := consulconfig.GetInstance().GetConfig(strLanguagePond, option...)
	if language_pond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_pond_str), &tblLanguagePond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_pond", errUnmarshal)
		return nil
	}
	storeLanguagePond.Store(fitKey, tblLanguagePond)
	return tblLanguagePond[id]
}

func LoadAllLanguagePondCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguagePond, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguagePond", successChannels)
	return nil
}

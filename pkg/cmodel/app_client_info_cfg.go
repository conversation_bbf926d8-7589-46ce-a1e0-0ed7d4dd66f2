// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AppClientInfo struct {
	Id                  int64  `json:"id"`
	GooglePlayStoreId   string `json:"googlePlayStoreId"`
	GooglePlayStoreName string `json:"googlePlayStoreName"`
	AppstoreStoreId     string `json:"appstoreStoreId"`
	EnableLog           string `json:"enableLog"`
}

var lockAppClientInfo sync.RWMutex
var storeAppClientInfo sync.Map
var strAppClientInfo string = "app_client_info"

func InitAppClientInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAppClientInfo, watchAppClientInfoFunc)
	return LoadAllAppClientInfoCfg()
}

func fixKeyAppClientInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAppClientInfo)
}
func watchAppClientInfoFunc(key string, js string) {
	store, ok := storeAppClientInfo.Load(key)
	if !ok {
		store = &AppClientInfo{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAppClientInfo.Store(key, store)
}

func GetAppClientInfo(option ...consulconfig.Option) *AppClientInfo {
	fitKey := fixKeyAppClientInfo(option...)
	store, ok := storeAppClientInfo.Load(fitKey)
	if ok {
		tblAppClientInfo, ok := store.(*AppClientInfo)
		if ok {
			return tblAppClientInfo
		}
	}
	lockAppClientInfo.Lock()
	defer lockAppClientInfo.Unlock()
	store, ok = storeAppClientInfo.Load(fitKey)
	if ok {
		tblAppClientInfo, ok := store.(*AppClientInfo)
		if ok {
			return tblAppClientInfo
		}
	}
	tblAppClientInfo := &AppClientInfo{}
	app_client_info_str, err := consulconfig.GetInstance().GetConfig(strAppClientInfo, option...)
	if app_client_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(app_client_info_str), &tblAppClientInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strAppClientInfo, errUnmarshal, app_client_info_str)
		return nil
	}
	storeAppClientInfo.Store(fitKey, tblAppClientInfo)
	return tblAppClientInfo
}

func LoadAllAppClientInfoCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAppClientInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AppClientInfo", successChannels)
	return nil
}

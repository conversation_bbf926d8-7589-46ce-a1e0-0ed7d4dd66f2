// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishLogInfo struct {
	Id        int64   `json:"id"`
	Mark      string  `json:"mark"`
	InfoDesc  int64   `json:"infoDesc"`
	ThemeType int32   `json:"themeType"`
	TabType   []int32 `json:"tabType"`
}

var lockFishLogInfo sync.RWMutex
var storeFishLogInfo sync.Map
var strFishLogInfo string = "fish_log_info"

func InitFishLogInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishLogInfo, watchFishLogInfoFunc)
	return LoadAllFishLogInfoCfg()
}

func fixKeyFishLogInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishLogInfo)
}
func watchFishLogInfoFunc(key string, js string) {
	mapFishLogInfo := make(map[int64]*FishLogInfo)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishLogInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishLogInfo.Store(key, mapFishLogInfo)
}

func GetAllFishLogInfo(option ...consulconfig.Option) map[int64]*FishLogInfo {
	fitKey := fixKeyFishLogInfo(option...)
	store, ok := storeFishLogInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishLogInfo)
		if ok {
			return storeMap
		}
	}
	lockFishLogInfo.Lock()
	defer lockFishLogInfo.Unlock()
	store, ok = storeFishLogInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishLogInfo)
		if ok {
			return storeMap
		}
	}
	tblFishLogInfo := make(map[int64]*FishLogInfo)
	fish_log_info_str, err := consulconfig.GetInstance().GetConfig(strFishLogInfo, option...)
	if fish_log_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_log_info_str), &tblFishLogInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_log_info", errUnmarshal)
		return nil
	}
	storeFishLogInfo.Store(fitKey, tblFishLogInfo)
	return tblFishLogInfo
}

func GetFishLogInfo(id int64, option ...consulconfig.Option) *FishLogInfo {
	fitKey := fixKeyFishLogInfo(option...)
	store, ok := storeFishLogInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishLogInfo)
		if ok {
			return storeMap[id]
		}
	}
	lockFishLogInfo.Lock()
	defer lockFishLogInfo.Unlock()
	store, ok = storeFishLogInfo.Load(fitKey)
	if ok {
		tblFishLogInfo, ok := store.(*FishLogInfo)
		if ok {
			return tblFishLogInfo
		}
	}
	tblFishLogInfo := make(map[int64]*FishLogInfo)
	fish_log_info_str, err := consulconfig.GetInstance().GetConfig(strFishLogInfo, option...)
	if fish_log_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_log_info_str), &tblFishLogInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_log_info", errUnmarshal)
		return nil
	}
	storeFishLogInfo.Store(fitKey, tblFishLogInfo)
	return tblFishLogInfo[id]
}

func LoadAllFishLogInfoCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishLogInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishLogInfo", successChannels)
	return nil
}

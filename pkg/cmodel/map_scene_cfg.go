// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type MapScene struct {
	Id            int64  `json:"id"`
	Name          string `json:"name"`
	Desc          int64  `json:"desc"`
	AssetId       string `json:"assetId"`
	OriginOffsetX int64  `json:"originOffsetX"`
	OriginOffsetY int64  `json:"originOffsetY"`
	OffsetX       int64  `json:"offsetX"`
	OffsetY       int64  `json:"offsetY"`
	SizeX         int64  `json:"sizeX"`
	SizeY         int64  `json:"sizeY"`
	Rotate        int64  `json:"rotate"`
	Mark          string `json:"mark"`
}

var lockMapScene sync.RWMutex
var storeMapScene sync.Map
var strMapScene string = "map_scene"

func InitMapSceneCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strMapScene, watchMapSceneFunc)
	return LoadAllMapSceneCfg()
}

func fixKeyMapScene(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strMapScene)
}
func watchMapSceneFunc(key string, js string) {
	mapMapScene := make(map[int64]*MapScene)
	errUnmarshal := json.Unmarshal([]byte(js), &mapMapScene)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeMapScene.Store(key, mapMapScene)
}

func GetAllMapScene(option ...consulconfig.Option) map[int64]*MapScene {
	fitKey := fixKeyMapScene(option...)
	store, ok := storeMapScene.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapScene)
		if ok {
			return storeMap
		}
	}
	lockMapScene.Lock()
	defer lockMapScene.Unlock()
	store, ok = storeMapScene.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapScene)
		if ok {
			return storeMap
		}
	}
	tblMapScene := make(map[int64]*MapScene)
	map_scene_str, err := consulconfig.GetInstance().GetConfig(strMapScene, option...)
	if map_scene_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_scene_str), &tblMapScene)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_scene", errUnmarshal)
		return nil
	}
	storeMapScene.Store(fitKey, tblMapScene)
	return tblMapScene
}

func GetMapScene(id int64, option ...consulconfig.Option) *MapScene {
	fitKey := fixKeyMapScene(option...)
	store, ok := storeMapScene.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*MapScene)
		if ok {
			return storeMap[id]
		}
	}
	lockMapScene.Lock()
	defer lockMapScene.Unlock()
	store, ok = storeMapScene.Load(fitKey)
	if ok {
		tblMapScene, ok := store.(*MapScene)
		if ok {
			return tblMapScene
		}
	}
	tblMapScene := make(map[int64]*MapScene)
	map_scene_str, err := consulconfig.GetInstance().GetConfig(strMapScene, option...)
	if map_scene_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(map_scene_str), &tblMapScene)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "map_scene", errUnmarshal)
		return nil
	}
	storeMapScene.Store(fitKey, tblMapScene)
	return tblMapScene[id]
}

func LoadAllMapSceneCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strMapScene, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "MapScene", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Lures struct {
	Id            int64   `json:"id"`
	Brand         int32   `json:"brand"`
	Series        int32   `json:"series"`
	SizeName      string  `json:"sizeName"`
	SeriesDes     string  `json:"seriesDes"`
	SubType       int32   `json:"subType"`
	Name          string  `json:"name"`
	Mark          string  `json:"mark"`
	ArtId         string  `json:"artId"`
	Durability    int32   `json:"durability"`
	Weight        int32   `json:"weight"`
	Length        int32   `json:"length"`
	StaticType    int32   `json:"staticType"`
	LengthFactor  float32 `json:"lengthFactor"`
	Buoyancy      float32 `json:"buoyancy"`
	WindageFactor float32 `json:"windageFactor"`
	SplashType    int32   `json:"splashType"`
	NoiseType     int32   `json:"noiseType"`
	LightingType  int32   `json:"lightingType"`
	BehaviorId    int32   `json:"behaviorId"`
	AffCoeff      int32   `json:"affCoeff"`
	AffTag        int32   `json:"affTag"`
	HookId        int64   `json:"hookId"`
	TrebleHooks   bool    `json:"trebleHooks"`
}

var lockLures sync.RWMutex
var storeLures sync.Map
var strLures string = "lures"

func InitLuresCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLures, watchLuresFunc)
	return LoadAllLuresCfg()
}

func fixKeyLures(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLures)
}
func watchLuresFunc(key string, js string) {
	mapLures := make(map[int64]*Lures)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLures)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLures.Store(key, mapLures)
}

func GetAllLures(option ...consulconfig.Option) map[int64]*Lures {
	fitKey := fixKeyLures(option...)
	store, ok := storeLures.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Lures)
		if ok {
			return storeMap
		}
	}
	lockLures.Lock()
	defer lockLures.Unlock()
	store, ok = storeLures.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Lures)
		if ok {
			return storeMap
		}
	}
	tblLures := make(map[int64]*Lures)
	lures_str, err := consulconfig.GetInstance().GetConfig(strLures, option...)
	if lures_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(lures_str), &tblLures)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "lures", errUnmarshal)
		return nil
	}
	storeLures.Store(fitKey, tblLures)
	return tblLures
}

func GetLures(id int64, option ...consulconfig.Option) *Lures {
	fitKey := fixKeyLures(option...)
	store, ok := storeLures.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Lures)
		if ok {
			return storeMap[id]
		}
	}
	lockLures.Lock()
	defer lockLures.Unlock()
	store, ok = storeLures.Load(fitKey)
	if ok {
		tblLures, ok := store.(*Lures)
		if ok {
			return tblLures
		}
	}
	tblLures := make(map[int64]*Lures)
	lures_str, err := consulconfig.GetInstance().GetConfig(strLures, option...)
	if lures_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(lures_str), &tblLures)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "lures", errUnmarshal)
		return nil
	}
	storeLures.Store(fitKey, tblLures)
	return tblLures[id]
}

func LoadAllLuresCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLures, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Lures", successChannels)
	return nil
}

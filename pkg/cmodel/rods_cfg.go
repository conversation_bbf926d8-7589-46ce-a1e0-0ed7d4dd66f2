// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RodsBaitWeight struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type RodsCastWeight struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type Rods struct {
	Id               int64          `json:"id"`
	Brand            int32          `json:"brand"`
	Series           int32          `json:"series"`
	SizeName         string         `json:"sizeName"`
	SeriesDes        string         `json:"seriesDes"`
	SubType          int32          `json:"subType"`
	Name             string         `json:"name"`
	Mark             string         `json:"mark"`
	ArtId            string         `json:"artId"`
	Durability       int32          `json:"durability"`
	DurabilityCoeff  int32          `json:"durabilityCoeff"`
	DurabilityTag    int32          `json:"durabilityTag"`
	Weight           int32          `json:"weight"`
	WeightCoeff      int32          `json:"weightCoeff"`
	WeightTag        int32          `json:"weightTag"`
	BaitWeight       RodsBaitWeight `json:"baitWeight"`
	CastWeight       RodsCastWeight `json:"castWeight"`
	Length           int32          `json:"length"`
	Action           int32          `json:"action"`
	HardnessType     int32          `json:"hardnessType"`
	MaxCastDistance  int32          `json:"maxCastDistance"`
	DistanceCoeff    int32          `json:"distanceCoeff"`
	DistanceTag      int32          `json:"distanceTag"`
	EnergyCostFactor float32        `json:"energyCostFactor"`
}

var lockRods sync.RWMutex
var storeRods sync.Map
var strRods string = "rods"

func InitRodsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRods, watchRodsFunc)
	return LoadAllRodsCfg()
}

func fixKeyRods(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRods)
}
func watchRodsFunc(key string, js string) {
	mapRods := make(map[int64]*Rods)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRods)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRods.Store(key, mapRods)
}

func GetAllRods(option ...consulconfig.Option) map[int64]*Rods {
	fitKey := fixKeyRods(option...)
	store, ok := storeRods.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Rods)
		if ok {
			return storeMap
		}
	}
	lockRods.Lock()
	defer lockRods.Unlock()
	store, ok = storeRods.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Rods)
		if ok {
			return storeMap
		}
	}
	tblRods := make(map[int64]*Rods)
	rods_str, err := consulconfig.GetInstance().GetConfig(strRods, option...)
	if rods_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rods_str), &tblRods)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rods", errUnmarshal)
		return nil
	}
	storeRods.Store(fitKey, tblRods)
	return tblRods
}

func GetRods(id int64, option ...consulconfig.Option) *Rods {
	fitKey := fixKeyRods(option...)
	store, ok := storeRods.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Rods)
		if ok {
			return storeMap[id]
		}
	}
	lockRods.Lock()
	defer lockRods.Unlock()
	store, ok = storeRods.Load(fitKey)
	if ok {
		tblRods, ok := store.(*Rods)
		if ok {
			return tblRods
		}
	}
	tblRods := make(map[int64]*Rods)
	rods_str, err := consulconfig.GetInstance().GetConfig(strRods, option...)
	if rods_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rods_str), &tblRods)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rods", errUnmarshal)
		return nil
	}
	storeRods.Store(fitKey, tblRods)
	return tblRods[id]
}

func LoadAllRodsCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRods, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Rods", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskProgress struct {
	Id           int64  `json:"id"`
	NameLanguage int64  `json:"nameLanguage"`
	Mark         string `json:"mark"`
	Type         int32  `json:"type"`
	SubId        int64  `json:"subId"`
	Score        int64  `json:"score"`
	Reward       int64  `json:"reward"`
}

var lockTaskProgress sync.RWMutex
var storeTaskProgress sync.Map
var strTaskProgress string = "task_progress"

func InitTaskProgressCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskProgress, watchTaskProgressFunc)
	return LoadAllTaskProgressCfg()
}

func fixKeyTaskProgress(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskProgress)
}
func watchTaskProgressFunc(key string, js string) {
	mapTaskProgress := make(map[int64]*TaskProgress)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskProgress)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskProgress.Store(key, mapTaskProgress)
}

func GetAllTaskProgress(option ...consulconfig.Option) map[int64]*TaskProgress {
	fitKey := fixKeyTaskProgress(option...)
	store, ok := storeTaskProgress.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskProgress)
		if ok {
			return storeMap
		}
	}
	lockTaskProgress.Lock()
	defer lockTaskProgress.Unlock()
	store, ok = storeTaskProgress.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskProgress)
		if ok {
			return storeMap
		}
	}
	tblTaskProgress := make(map[int64]*TaskProgress)
	task_progress_str, err := consulconfig.GetInstance().GetConfig(strTaskProgress, option...)
	if task_progress_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_progress_str), &tblTaskProgress)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_progress", errUnmarshal)
		return nil
	}
	storeTaskProgress.Store(fitKey, tblTaskProgress)
	return tblTaskProgress
}

func GetTaskProgress(id int64, option ...consulconfig.Option) *TaskProgress {
	fitKey := fixKeyTaskProgress(option...)
	store, ok := storeTaskProgress.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskProgress)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskProgress.Lock()
	defer lockTaskProgress.Unlock()
	store, ok = storeTaskProgress.Load(fitKey)
	if ok {
		tblTaskProgress, ok := store.(*TaskProgress)
		if ok {
			return tblTaskProgress
		}
	}
	tblTaskProgress := make(map[int64]*TaskProgress)
	task_progress_str, err := consulconfig.GetInstance().GetConfig(strTaskProgress, option...)
	if task_progress_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_progress_str), &tblTaskProgress)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_progress", errUnmarshal)
		return nil
	}
	storeTaskProgress.Store(fitKey, tblTaskProgress)
	return tblTaskProgress[id]
}

func LoadAllTaskProgressCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskProgress, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskProgress", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Lines struct {
	Id              int64   `json:"id"`
	Brand           int32   `json:"brand"`
	Series          int32   `json:"series"`
	SizeName        string  `json:"sizeName"`
	SeriesDes       string  `json:"seriesDes"`
	SubType         int32   `json:"subType"`
	Name            string  `json:"name"`
	Mark            string  `json:"mark"`
	ArtId           string  `json:"artId"`
	SaleType        int32   `json:"saleType"`
	Durability      int32   `json:"durability"`
	DurabilityCoeff int32   `json:"durabilityCoeff"`
	DurabilityTag   int32   `json:"durabilityTag"`
	Diameter        int32   `json:"diameter"`
	Color           string  `json:"color"`
	Opacity         int32   `json:"opacity"`
	Length          int32   `json:"length"`
	Drag            int32   `json:"drag"`
	FrictionFactor  float32 `json:"frictionFactor"`
	ThicknessFactor float32 `json:"thicknessFactor"`
	TensionFactor   float32 `json:"tensionFactor"`
	Weight          int32   `json:"weight"`
	DistanceCoeff   int32   `json:"distanceCoeff"`
	DistanceTag     int32   `json:"distanceTag"`
	Crypticity      int32   `json:"crypticity"`
	CrypticityTag   int32   `json:"crypticityTag"`
}

var lockLines sync.RWMutex
var storeLines sync.Map
var strLines string = "lines"

func InitLinesCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLines, watchLinesFunc)
	return LoadAllLinesCfg()
}

func fixKeyLines(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLines)
}
func watchLinesFunc(key string, js string) {
	mapLines := make(map[int64]*Lines)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLines)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLines.Store(key, mapLines)
}

func GetAllLines(option ...consulconfig.Option) map[int64]*Lines {
	fitKey := fixKeyLines(option...)
	store, ok := storeLines.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Lines)
		if ok {
			return storeMap
		}
	}
	lockLines.Lock()
	defer lockLines.Unlock()
	store, ok = storeLines.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Lines)
		if ok {
			return storeMap
		}
	}
	tblLines := make(map[int64]*Lines)
	lines_str, err := consulconfig.GetInstance().GetConfig(strLines, option...)
	if lines_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(lines_str), &tblLines)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "lines", errUnmarshal)
		return nil
	}
	storeLines.Store(fitKey, tblLines)
	return tblLines
}

func GetLines(id int64, option ...consulconfig.Option) *Lines {
	fitKey := fixKeyLines(option...)
	store, ok := storeLines.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Lines)
		if ok {
			return storeMap[id]
		}
	}
	lockLines.Lock()
	defer lockLines.Unlock()
	store, ok = storeLines.Load(fitKey)
	if ok {
		tblLines, ok := store.(*Lines)
		if ok {
			return tblLines
		}
	}
	tblLines := make(map[int64]*Lines)
	lines_str, err := consulconfig.GetInstance().GetConfig(strLines, option...)
	if lines_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(lines_str), &tblLines)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "lines", errUnmarshal)
		return nil
	}
	storeLines.Store(fitKey, tblLines)
	return tblLines[id]
}

func LoadAllLinesCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLines, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Lines", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AnnPopUp struct {
	Id              int64  `json:"id"`
	Name            string `json:"name"`
	Enable          bool   `json:"enable"`
	Scenes          int64  `json:"scenes"`
	Priority        int64  `json:"priority"`
	DisplayStrategy int64  `json:"displayStrategy"`
	Text            string `json:"text"`
	StartTime       int64  `json:"startTime"`
	EndTime         int64  `json:"endTime"`
}

var lockAnnPopUp sync.RWMutex
var storeAnnPopUp sync.Map
var strAnnPopUp string = "ann_pop_up"

func InitAnnPopUpCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAnnPopUp, watchAnnPopUpFunc)
	return LoadAllAnnPopUpCfg()
}

func fixKeyAnnPopUp(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAnnPopUp)
}
func watchAnnPopUpFunc(key string, js string) {
	mapAnnPopUp := make(map[int64]*AnnPopUp)
	errUnmarshal := json.Unmarshal([]byte(js), &mapAnnPopUp)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAnnPopUp.Store(key, mapAnnPopUp)
}

func GetAllAnnPopUp(option ...consulconfig.Option) map[int64]*AnnPopUp {
	fitKey := fixKeyAnnPopUp(option...)
	store, ok := storeAnnPopUp.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*AnnPopUp)
		if ok {
			return storeMap
		}
	}
	lockAnnPopUp.Lock()
	defer lockAnnPopUp.Unlock()
	store, ok = storeAnnPopUp.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*AnnPopUp)
		if ok {
			return storeMap
		}
	}
	tblAnnPopUp := make(map[int64]*AnnPopUp)
	ann_pop_up_str, err := consulconfig.GetInstance().GetConfig(strAnnPopUp, option...)
	if ann_pop_up_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(ann_pop_up_str), &tblAnnPopUp)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "ann_pop_up", errUnmarshal)
		return nil
	}
	storeAnnPopUp.Store(fitKey, tblAnnPopUp)
	return tblAnnPopUp
}

func GetAnnPopUp(id int64, option ...consulconfig.Option) *AnnPopUp {
	fitKey := fixKeyAnnPopUp(option...)
	store, ok := storeAnnPopUp.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*AnnPopUp)
		if ok {
			return storeMap[id]
		}
	}
	lockAnnPopUp.Lock()
	defer lockAnnPopUp.Unlock()
	store, ok = storeAnnPopUp.Load(fitKey)
	if ok {
		tblAnnPopUp, ok := store.(*AnnPopUp)
		if ok {
			return tblAnnPopUp
		}
	}
	tblAnnPopUp := make(map[int64]*AnnPopUp)
	ann_pop_up_str, err := consulconfig.GetInstance().GetConfig(strAnnPopUp, option...)
	if ann_pop_up_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(ann_pop_up_str), &tblAnnPopUp)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "ann_pop_up", errUnmarshal)
		return nil
	}
	storeAnnPopUp.Store(fitKey, tblAnnPopUp)
	return tblAnnPopUp[id]
}

func LoadAllAnnPopUpCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAnnPopUp, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AnnPopUp", successChannels)
	return nil
}

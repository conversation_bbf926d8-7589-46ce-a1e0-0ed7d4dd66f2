// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type EnergyBasic struct {
	Id                      int64   `json:"id"`
	EnergyToVitalityNormal  float32 `json:"energyToVitalityNormal"`
	EnergyToVitalityBreak   float32 `json:"energyToVitalityBreak"`
	BasicEnergy             float32 `json:"basicEnergy"`
	EnergyLvUpAdd           float32 `json:"energyLvUpAdd"`
	BasicEnergyRecoverSpeed float32 `json:"basicEnergyRecoverSpeed"`
	RecoverSpeedLvUpAdd     float32 `json:"recoverSpeedLvUpAdd"`
}

var lockEnergyBasic sync.RWMutex
var storeEnergyBasic sync.Map
var strEnergyBasic string = "energy_basic"

func InitEnergyBasicCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strEnergyBasic, watchEnergyBasicFunc)
	return LoadAllEnergyBasicCfg()
}

func fixKeyEnergyBasic(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strEnergyBasic)
}
func watchEnergyBasicFunc(key string, js string) {
	store, ok := storeEnergyBasic.Load(key)
	if !ok {
		store = &EnergyBasic{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeEnergyBasic.Store(key, store)
}

func GetEnergyBasic(option ...consulconfig.Option) *EnergyBasic {
	fitKey := fixKeyEnergyBasic(option...)
	store, ok := storeEnergyBasic.Load(fitKey)
	if ok {
		tblEnergyBasic, ok := store.(*EnergyBasic)
		if ok {
			return tblEnergyBasic
		}
	}
	lockEnergyBasic.Lock()
	defer lockEnergyBasic.Unlock()
	store, ok = storeEnergyBasic.Load(fitKey)
	if ok {
		tblEnergyBasic, ok := store.(*EnergyBasic)
		if ok {
			return tblEnergyBasic
		}
	}
	tblEnergyBasic := &EnergyBasic{}
	energy_basic_str, err := consulconfig.GetInstance().GetConfig(strEnergyBasic, option...)
	if energy_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(energy_basic_str), &tblEnergyBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strEnergyBasic, errUnmarshal, energy_basic_str)
		return nil
	}
	storeEnergyBasic.Store(fitKey, tblEnergyBasic)
	return tblEnergyBasic
}

func LoadAllEnergyBasicCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strEnergyBasic, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "EnergyBasic", successChannels)
	return nil
}

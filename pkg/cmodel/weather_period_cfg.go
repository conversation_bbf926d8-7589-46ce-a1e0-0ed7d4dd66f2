// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WeatherPeriod struct {
	Id      int64   `json:"id"`
	Name    string  `json:"name"`
	Desc    string  `json:"desc"`
	Periods []int64 `json:"periods"`
}

var lockWeatherPeriod sync.RWMutex
var storeWeatherPeriod sync.Map
var strWeatherPeriod string = "weather_period"

func InitWeatherPeriodCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWeatherPeriod, watchWeatherPeriodFunc)
	return LoadAllWeatherPeriodCfg()
}

func fixKeyWeatherPeriod(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWeatherPeriod)
}
func watchWeatherPeriodFunc(key string, js string) {
	mapWeatherPeriod := make(map[int64]*WeatherPeriod)
	errUnmarshal := json.Unmarshal([]byte(js), &mapWeatherPeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWeatherPeriod.Store(key, mapWeatherPeriod)
}

func GetAllWeatherPeriod(option ...consulconfig.Option) map[int64]*WeatherPeriod {
	fitKey := fixKeyWeatherPeriod(option...)
	store, ok := storeWeatherPeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherPeriod)
		if ok {
			return storeMap
		}
	}
	lockWeatherPeriod.Lock()
	defer lockWeatherPeriod.Unlock()
	store, ok = storeWeatherPeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherPeriod)
		if ok {
			return storeMap
		}
	}
	tblWeatherPeriod := make(map[int64]*WeatherPeriod)
	weather_period_str, err := consulconfig.GetInstance().GetConfig(strWeatherPeriod, option...)
	if weather_period_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_period_str), &tblWeatherPeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_period", errUnmarshal)
		return nil
	}
	storeWeatherPeriod.Store(fitKey, tblWeatherPeriod)
	return tblWeatherPeriod
}

func GetWeatherPeriod(id int64, option ...consulconfig.Option) *WeatherPeriod {
	fitKey := fixKeyWeatherPeriod(option...)
	store, ok := storeWeatherPeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherPeriod)
		if ok {
			return storeMap[id]
		}
	}
	lockWeatherPeriod.Lock()
	defer lockWeatherPeriod.Unlock()
	store, ok = storeWeatherPeriod.Load(fitKey)
	if ok {
		tblWeatherPeriod, ok := store.(*WeatherPeriod)
		if ok {
			return tblWeatherPeriod
		}
	}
	tblWeatherPeriod := make(map[int64]*WeatherPeriod)
	weather_period_str, err := consulconfig.GetInstance().GetConfig(strWeatherPeriod, option...)
	if weather_period_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_period_str), &tblWeatherPeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_period", errUnmarshal)
		return nil
	}
	storeWeatherPeriod.Store(fitKey, tblWeatherPeriod)
	return tblWeatherPeriod[id]
}

func LoadAllWeatherPeriodCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strWeatherPeriod, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WeatherPeriod", successChannels)
	return nil
}

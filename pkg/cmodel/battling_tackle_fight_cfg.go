// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BattlingTackleFight struct {
	ItemQuality                 int32   `json:"itemQuality"`
	TackleQualityBattlingFactor float32 `json:"tackleQualityBattlingFactor"`
}

var lockBattlingTackleFight sync.RWMutex
var storeBattlingTackleFight sync.Map
var strBattlingTackleFight string = "battling_tackle_fight"

func InitBattlingTackleFightCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBattlingTackleFight, watchBattlingTackleFightFunc)
	return LoadAllBattlingTackleFightCfg()
}

func fixKeyBattlingTackleFight(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBattlingTackleFight)
}
func watchBattlingTackleFightFunc(key string, js string) {
	mapBattlingTackleFight := make(map[int64]*BattlingTackleFight)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBattlingTackleFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBattlingTackleFight.Store(key, mapBattlingTackleFight)
}

func GetAllBattlingTackleFight(option ...consulconfig.Option) map[int64]*BattlingTackleFight {
	fitKey := fixKeyBattlingTackleFight(option...)
	store, ok := storeBattlingTackleFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingTackleFight)
		if ok {
			return storeMap
		}
	}
	lockBattlingTackleFight.Lock()
	defer lockBattlingTackleFight.Unlock()
	store, ok = storeBattlingTackleFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingTackleFight)
		if ok {
			return storeMap
		}
	}
	tblBattlingTackleFight := make(map[int64]*BattlingTackleFight)
	battling_tackle_fight_str, err := consulconfig.GetInstance().GetConfig(strBattlingTackleFight, option...)
	if battling_tackle_fight_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(battling_tackle_fight_str), &tblBattlingTackleFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "battling_tackle_fight", errUnmarshal)
		return nil
	}
	storeBattlingTackleFight.Store(fitKey, tblBattlingTackleFight)
	return tblBattlingTackleFight
}

func GetBattlingTackleFight(id int64, option ...consulconfig.Option) *BattlingTackleFight {
	fitKey := fixKeyBattlingTackleFight(option...)
	store, ok := storeBattlingTackleFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingTackleFight)
		if ok {
			return storeMap[id]
		}
	}
	lockBattlingTackleFight.Lock()
	defer lockBattlingTackleFight.Unlock()
	store, ok = storeBattlingTackleFight.Load(fitKey)
	if ok {
		tblBattlingTackleFight, ok := store.(*BattlingTackleFight)
		if ok {
			return tblBattlingTackleFight
		}
	}
	tblBattlingTackleFight := make(map[int64]*BattlingTackleFight)
	battling_tackle_fight_str, err := consulconfig.GetInstance().GetConfig(strBattlingTackleFight, option...)
	if battling_tackle_fight_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(battling_tackle_fight_str), &tblBattlingTackleFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "battling_tackle_fight", errUnmarshal)
		return nil
	}
	storeBattlingTackleFight.Store(fitKey, tblBattlingTackleFight)
	return tblBattlingTackleFight[id]
}

func LoadAllBattlingTackleFightCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBattlingTackleFight, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BattlingTackleFight", successChannels)
	return nil
}

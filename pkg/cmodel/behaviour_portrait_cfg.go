// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BehaviourPortrait struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

var lockBehaviourPortrait sync.RWMutex
var storeBehaviourPortrait sync.Map
var strBehaviourPortrait string = "behaviour_portrait"

func InitBehaviourPortraitCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBehaviourPortrait, watchBehaviourPortraitFunc)
	return LoadAllBehaviourPortraitCfg()
}

func fixKeyBehaviourPortrait(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBehaviourPortrait)
}
func watchBehaviourPortraitFunc(key string, js string) {
	mapBehaviourPortrait := make(map[int64]*BehaviourPortrait)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBehaviourPortrait)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBehaviourPortrait.Store(key, mapBehaviourPortrait)
}

func GetAllBehaviourPortrait(option ...consulconfig.Option) map[int64]*BehaviourPortrait {
	fitKey := fixKeyBehaviourPortrait(option...)
	store, ok := storeBehaviourPortrait.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BehaviourPortrait)
		if ok {
			return storeMap
		}
	}
	lockBehaviourPortrait.Lock()
	defer lockBehaviourPortrait.Unlock()
	store, ok = storeBehaviourPortrait.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BehaviourPortrait)
		if ok {
			return storeMap
		}
	}
	tblBehaviourPortrait := make(map[int64]*BehaviourPortrait)
	behaviour_portrait_str, err := consulconfig.GetInstance().GetConfig(strBehaviourPortrait, option...)
	if behaviour_portrait_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(behaviour_portrait_str), &tblBehaviourPortrait)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "behaviour_portrait", errUnmarshal)
		return nil
	}
	storeBehaviourPortrait.Store(fitKey, tblBehaviourPortrait)
	return tblBehaviourPortrait
}

func GetBehaviourPortrait(id int64, option ...consulconfig.Option) *BehaviourPortrait {
	fitKey := fixKeyBehaviourPortrait(option...)
	store, ok := storeBehaviourPortrait.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BehaviourPortrait)
		if ok {
			return storeMap[id]
		}
	}
	lockBehaviourPortrait.Lock()
	defer lockBehaviourPortrait.Unlock()
	store, ok = storeBehaviourPortrait.Load(fitKey)
	if ok {
		tblBehaviourPortrait, ok := store.(*BehaviourPortrait)
		if ok {
			return tblBehaviourPortrait
		}
	}
	tblBehaviourPortrait := make(map[int64]*BehaviourPortrait)
	behaviour_portrait_str, err := consulconfig.GetInstance().GetConfig(strBehaviourPortrait, option...)
	if behaviour_portrait_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(behaviour_portrait_str), &tblBehaviourPortrait)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "behaviour_portrait", errUnmarshal)
		return nil
	}
	storeBehaviourPortrait.Store(fitKey, tblBehaviourPortrait)
	return tblBehaviourPortrait[id]
}

func LoadAllBehaviourPortraitCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBehaviourPortrait, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BehaviourPortrait", successChannels)
	return nil
}

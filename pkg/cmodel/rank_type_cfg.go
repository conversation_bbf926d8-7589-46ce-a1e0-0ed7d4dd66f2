// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RankType struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockRankType sync.RWMutex
var storeRankType sync.Map
var strRankType string = "rank_type"

func InitRankTypeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRankType, watchRankTypeFunc)
	return LoadAllRankTypeCfg()
}

func fixKeyRankType(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRankType)
}
func watchRankTypeFunc(key string, js string) {
	mapRankType := make(map[int64]*RankType)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRankType)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRankType.Store(key, mapRankType)
}

func GetAllRankType(option ...consulconfig.Option) map[int64]*RankType {
	fitKey := fixKeyRankType(option...)
	store, ok := storeRankType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RankType)
		if ok {
			return storeMap
		}
	}
	lockRankType.Lock()
	defer lockRankType.Unlock()
	store, ok = storeRankType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RankType)
		if ok {
			return storeMap
		}
	}
	tblRankType := make(map[int64]*RankType)
	rank_type_str, err := consulconfig.GetInstance().GetConfig(strRankType, option...)
	if rank_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rank_type_str), &tblRankType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rank_type", errUnmarshal)
		return nil
	}
	storeRankType.Store(fitKey, tblRankType)
	return tblRankType
}

func GetRankType(id int64, option ...consulconfig.Option) *RankType {
	fitKey := fixKeyRankType(option...)
	store, ok := storeRankType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RankType)
		if ok {
			return storeMap[id]
		}
	}
	lockRankType.Lock()
	defer lockRankType.Unlock()
	store, ok = storeRankType.Load(fitKey)
	if ok {
		tblRankType, ok := store.(*RankType)
		if ok {
			return tblRankType
		}
	}
	tblRankType := make(map[int64]*RankType)
	rank_type_str, err := consulconfig.GetInstance().GetConfig(strRankType, option...)
	if rank_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rank_type_str), &tblRankType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rank_type", errUnmarshal)
		return nil
	}
	storeRankType.Store(fitKey, tblRankType)
	return tblRankType[id]
}

func LoadAllRankTypeCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRankType, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RankType", successChannels)
	return nil
}

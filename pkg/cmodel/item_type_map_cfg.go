// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ItemTypeMap struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	CategoryId  int32  `json:"categoryId"`
	ItemType    int32  `json:"itemType"`
	ItemSubType int32  `json:"itemSubType"`
	LanguageId  int64  `json:"languageId"`
	Mark        string `json:"mark"`
}

var lockItemTypeMap sync.RWMutex
var storeItemTypeMap sync.Map
var strItemTypeMap string = "item_type_map"

func InitItemTypeMapCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItemTypeMap, watchItemTypeMapFunc)
	return LoadAllItemTypeMapCfg()
}

func fixKeyItemTypeMap(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItemTypeMap)
}
func watchItemTypeMapFunc(key string, js string) {
	mapItemTypeMap := make(map[int64]*ItemTypeMap)
	errUnmarshal := json.Unmarshal([]byte(js), &mapItemTypeMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItemTypeMap.Store(key, mapItemTypeMap)
}

func GetAllItemTypeMap(option ...consulconfig.Option) map[int64]*ItemTypeMap {
	fitKey := fixKeyItemTypeMap(option...)
	store, ok := storeItemTypeMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemTypeMap)
		if ok {
			return storeMap
		}
	}
	lockItemTypeMap.Lock()
	defer lockItemTypeMap.Unlock()
	store, ok = storeItemTypeMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemTypeMap)
		if ok {
			return storeMap
		}
	}
	tblItemTypeMap := make(map[int64]*ItemTypeMap)
	item_type_map_str, err := consulconfig.GetInstance().GetConfig(strItemTypeMap, option...)
	if item_type_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_type_map_str), &tblItemTypeMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_type_map", errUnmarshal)
		return nil
	}
	storeItemTypeMap.Store(fitKey, tblItemTypeMap)
	return tblItemTypeMap
}

func GetItemTypeMap(id int64, option ...consulconfig.Option) *ItemTypeMap {
	fitKey := fixKeyItemTypeMap(option...)
	store, ok := storeItemTypeMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemTypeMap)
		if ok {
			return storeMap[id]
		}
	}
	lockItemTypeMap.Lock()
	defer lockItemTypeMap.Unlock()
	store, ok = storeItemTypeMap.Load(fitKey)
	if ok {
		tblItemTypeMap, ok := store.(*ItemTypeMap)
		if ok {
			return tblItemTypeMap
		}
	}
	tblItemTypeMap := make(map[int64]*ItemTypeMap)
	item_type_map_str, err := consulconfig.GetInstance().GetConfig(strItemTypeMap, option...)
	if item_type_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_type_map_str), &tblItemTypeMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_type_map", errUnmarshal)
		return nil
	}
	storeItemTypeMap.Store(fitKey, tblItemTypeMap)
	return tblItemTypeMap[id]
}

func LoadAllItemTypeMapCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItemTypeMap, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ItemTypeMap", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Baits struct {
	Id            int64   `json:"id"`
	Brand         int32   `json:"brand"`
	Series        int32   `json:"series"`
	SizeName      string  `json:"sizeName"`
	SeriesDes     string  `json:"seriesDes"`
	SubType       int32   `json:"subType"`
	Name          string  `json:"name"`
	Mark          string  `json:"mark"`
	ArtId         string  `json:"artId"`
	Durability    int32   `json:"durability"`
	Weight        int32   `json:"weight"`
	Buoyancy      float32 `json:"buoyancy"`
	WindageFactor float32 `json:"windageFactor"`
	BehaviorId    int32   `json:"behaviorId"`
	HookId        int64   `json:"hookId"`
	LengthFactor  float32 `json:"lengthFactor"`
	SplashType    int32   `json:"splashType"`
}

var lockBaits sync.RWMutex
var storeBaits sync.Map
var strBaits string = "baits"

func InitBaitsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBaits, watchBaitsFunc)
	return LoadAllBaitsCfg()
}

func fixKeyBaits(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBaits)
}
func watchBaitsFunc(key string, js string) {
	mapBaits := make(map[int64]*Baits)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBaits)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBaits.Store(key, mapBaits)
}

func GetAllBaits(option ...consulconfig.Option) map[int64]*Baits {
	fitKey := fixKeyBaits(option...)
	store, ok := storeBaits.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Baits)
		if ok {
			return storeMap
		}
	}
	lockBaits.Lock()
	defer lockBaits.Unlock()
	store, ok = storeBaits.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Baits)
		if ok {
			return storeMap
		}
	}
	tblBaits := make(map[int64]*Baits)
	baits_str, err := consulconfig.GetInstance().GetConfig(strBaits, option...)
	if baits_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(baits_str), &tblBaits)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "baits", errUnmarshal)
		return nil
	}
	storeBaits.Store(fitKey, tblBaits)
	return tblBaits
}

func GetBaits(id int64, option ...consulconfig.Option) *Baits {
	fitKey := fixKeyBaits(option...)
	store, ok := storeBaits.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Baits)
		if ok {
			return storeMap[id]
		}
	}
	lockBaits.Lock()
	defer lockBaits.Unlock()
	store, ok = storeBaits.Load(fitKey)
	if ok {
		tblBaits, ok := store.(*Baits)
		if ok {
			return tblBaits
		}
	}
	tblBaits := make(map[int64]*Baits)
	baits_str, err := consulconfig.GetInstance().GetConfig(strBaits, option...)
	if baits_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(baits_str), &tblBaits)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "baits", errUnmarshal)
		return nil
	}
	storeBaits.Store(fitKey, tblBaits)
	return tblBaits[id]
}

func LoadAllBaitsCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBaits, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Baits", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PoseFloatEntitiesLowLevelScore struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type PoseFloatEntitiesHighLevelScore struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type PoseFloatEntitiesLevelFactor struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type PoseFloatEntities struct {
	Id             int32                           `json:"id"`
	Desc           string                          `json:"desc"`
	Type           int32                           `json:"type"`
	Baits          []int32                         `json:"baits"`
	LowLevelScore  PoseFloatEntitiesLowLevelScore  `json:"lowLevelScore"`
	HighLevelScore PoseFloatEntitiesHighLevelScore `json:"highLevelScore"`
	LevelFactor    PoseFloatEntitiesLevelFactor    `json:"levelFactor"`
	Priority       int32                           `json:"priority"`
	BreakDuration  float32                         `json:"breakDuration"`
}

var lockPoseFloatEntities sync.RWMutex
var storePoseFloatEntities sync.Map
var strPoseFloatEntities string = "pose_float_entities"

func InitPoseFloatEntitiesCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPoseFloatEntities, watchPoseFloatEntitiesFunc)
	return LoadAllPoseFloatEntitiesCfg()
}

func fixKeyPoseFloatEntities(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPoseFloatEntities)
}
func watchPoseFloatEntitiesFunc(key string, js string) {
	mapPoseFloatEntities := make(map[int64]*PoseFloatEntities)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPoseFloatEntities)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePoseFloatEntities.Store(key, mapPoseFloatEntities)
}

func GetAllPoseFloatEntities(option ...consulconfig.Option) map[int64]*PoseFloatEntities {
	fitKey := fixKeyPoseFloatEntities(option...)
	store, ok := storePoseFloatEntities.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseFloatEntities)
		if ok {
			return storeMap
		}
	}
	lockPoseFloatEntities.Lock()
	defer lockPoseFloatEntities.Unlock()
	store, ok = storePoseFloatEntities.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseFloatEntities)
		if ok {
			return storeMap
		}
	}
	tblPoseFloatEntities := make(map[int64]*PoseFloatEntities)
	pose_float_entities_str, err := consulconfig.GetInstance().GetConfig(strPoseFloatEntities, option...)
	if pose_float_entities_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_float_entities_str), &tblPoseFloatEntities)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_float_entities", errUnmarshal)
		return nil
	}
	storePoseFloatEntities.Store(fitKey, tblPoseFloatEntities)
	return tblPoseFloatEntities
}

func GetPoseFloatEntities(id int64, option ...consulconfig.Option) *PoseFloatEntities {
	fitKey := fixKeyPoseFloatEntities(option...)
	store, ok := storePoseFloatEntities.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseFloatEntities)
		if ok {
			return storeMap[id]
		}
	}
	lockPoseFloatEntities.Lock()
	defer lockPoseFloatEntities.Unlock()
	store, ok = storePoseFloatEntities.Load(fitKey)
	if ok {
		tblPoseFloatEntities, ok := store.(*PoseFloatEntities)
		if ok {
			return tblPoseFloatEntities
		}
	}
	tblPoseFloatEntities := make(map[int64]*PoseFloatEntities)
	pose_float_entities_str, err := consulconfig.GetInstance().GetConfig(strPoseFloatEntities, option...)
	if pose_float_entities_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_float_entities_str), &tblPoseFloatEntities)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_float_entities", errUnmarshal)
		return nil
	}
	storePoseFloatEntities.Store(fitKey, tblPoseFloatEntities)
	return tblPoseFloatEntities[id]
}

func LoadAllPoseFloatEntitiesCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPoseFloatEntities, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PoseFloatEntities", successChannels)
	return nil
}

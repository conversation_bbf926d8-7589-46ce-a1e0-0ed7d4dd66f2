// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BaitTypeAffinity struct {
	Id                 int64   `json:"id"`
	BaitTypeCoeffGroup int64   `json:"baitTypeCoeffGroup"`
	BaitSubType        int32   `json:"baitSubType"`
	Coeff              float64 `json:"coeff"`
	PoseGroup          int64   `json:"poseGroup"`
	Mark               string  `json:"mark"`
}

var lockBaitTypeAffinity sync.RWMutex
var storeBaitTypeAffinity sync.Map
var strBaitTypeAffinity string = "bait_type_affinity"

func InitBaitTypeAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBaitTypeAffinity, watchBaitTypeAffinityFunc)
	return LoadAllBaitTypeAffinityCfg()
}

func fixKeyBaitTypeAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBaitTypeAffinity)
}
func watchBaitTypeAffinityFunc(key string, js string) {
	mapBaitTypeAffinity := make(map[int64]*BaitTypeAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBaitTypeAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBaitTypeAffinity.Store(key, mapBaitTypeAffinity)
}

func GetAllBaitTypeAffinity(option ...consulconfig.Option) map[int64]*BaitTypeAffinity {
	fitKey := fixKeyBaitTypeAffinity(option...)
	store, ok := storeBaitTypeAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitTypeAffinity)
		if ok {
			return storeMap
		}
	}
	lockBaitTypeAffinity.Lock()
	defer lockBaitTypeAffinity.Unlock()
	store, ok = storeBaitTypeAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitTypeAffinity)
		if ok {
			return storeMap
		}
	}
	tblBaitTypeAffinity := make(map[int64]*BaitTypeAffinity)
	bait_type_affinity_str, err := consulconfig.GetInstance().GetConfig(strBaitTypeAffinity, option...)
	if bait_type_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bait_type_affinity_str), &tblBaitTypeAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bait_type_affinity", errUnmarshal)
		return nil
	}
	storeBaitTypeAffinity.Store(fitKey, tblBaitTypeAffinity)
	return tblBaitTypeAffinity
}

func GetBaitTypeAffinity(id int64, option ...consulconfig.Option) *BaitTypeAffinity {
	fitKey := fixKeyBaitTypeAffinity(option...)
	store, ok := storeBaitTypeAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitTypeAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockBaitTypeAffinity.Lock()
	defer lockBaitTypeAffinity.Unlock()
	store, ok = storeBaitTypeAffinity.Load(fitKey)
	if ok {
		tblBaitTypeAffinity, ok := store.(*BaitTypeAffinity)
		if ok {
			return tblBaitTypeAffinity
		}
	}
	tblBaitTypeAffinity := make(map[int64]*BaitTypeAffinity)
	bait_type_affinity_str, err := consulconfig.GetInstance().GetConfig(strBaitTypeAffinity, option...)
	if bait_type_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bait_type_affinity_str), &tblBaitTypeAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bait_type_affinity", errUnmarshal)
		return nil
	}
	storeBaitTypeAffinity.Store(fitKey, tblBaitTypeAffinity)
	return tblBaitTypeAffinity[id]
}

func LoadAllBaitTypeAffinityCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBaitTypeAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BaitTypeAffinity", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskConst struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockTaskConst sync.RWMutex
var storeTaskConst sync.Map
var strTaskConst string = "task_const"

func InitTaskConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskConst, watchTaskConstFunc)
	return LoadAllTaskConstCfg()
}

func fixKeyTaskConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskConst)
}
func watchTaskConstFunc(key string, js string) {
	mapTaskConst := make(map[int64]*TaskConst)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskConst.Store(key, mapTaskConst)
}

func GetAllTaskConst(option ...consulconfig.Option) map[int64]*TaskConst {
	fitKey := fixKeyTaskConst(option...)
	store, ok := storeTaskConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskConst)
		if ok {
			return storeMap
		}
	}
	lockTaskConst.Lock()
	defer lockTaskConst.Unlock()
	store, ok = storeTaskConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskConst)
		if ok {
			return storeMap
		}
	}
	tblTaskConst := make(map[int64]*TaskConst)
	task_const_str, err := consulconfig.GetInstance().GetConfig(strTaskConst, option...)
	if task_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_const_str), &tblTaskConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_const", errUnmarshal)
		return nil
	}
	storeTaskConst.Store(fitKey, tblTaskConst)
	return tblTaskConst
}

func GetTaskConst(id int64, option ...consulconfig.Option) *TaskConst {
	fitKey := fixKeyTaskConst(option...)
	store, ok := storeTaskConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskConst)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskConst.Lock()
	defer lockTaskConst.Unlock()
	store, ok = storeTaskConst.Load(fitKey)
	if ok {
		tblTaskConst, ok := store.(*TaskConst)
		if ok {
			return tblTaskConst
		}
	}
	tblTaskConst := make(map[int64]*TaskConst)
	task_const_str, err := consulconfig.GetInstance().GetConfig(strTaskConst, option...)
	if task_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_const_str), &tblTaskConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_const", errUnmarshal)
		return nil
	}
	storeTaskConst.Store(fitKey, tblTaskConst)
	return tblTaskConst[id]
}

func LoadAllTaskConstCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskConst", successChannels)
	return nil
}

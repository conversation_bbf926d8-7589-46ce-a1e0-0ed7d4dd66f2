// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PoseLureEntitiesReelSection struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type PoseLureEntitiesDuration struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type PoseLureEntitiesScoreRange struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type PoseLureEntities struct {
	Id                int32                       `json:"id"`
	Desc              string                      `json:"desc"`
	Type              int32                       `json:"type"`
	Baits             []int32                     `json:"baits"`
	Layer             []int32                     `json:"layer"`
	ReelSection       PoseLureEntitiesReelSection `json:"reelSection"`
	Duration          PoseLureEntitiesDuration    `json:"duration"`
	KeepDirection     bool                        `json:"keepDirection"`
	KeepSpeed         bool                        `json:"keepSpeed"`
	LiftActions       []int32                     `json:"liftActions"`
	LastDuration      float32                     `json:"lastDuration"`
	Priority          int32                       `json:"priority"`
	BreakDuration     float32                     `json:"breakDuration"`
	BaseScore         int32                       `json:"baseScore"`
	ScoreRange        PoseLureEntitiesScoreRange  `json:"scoreRange"`
	ResumeScoreFactor float32                     `json:"resumeScoreFactor"`
}

var lockPoseLureEntities sync.RWMutex
var storePoseLureEntities sync.Map
var strPoseLureEntities string = "pose_lure_entities"

func InitPoseLureEntitiesCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPoseLureEntities, watchPoseLureEntitiesFunc)
	return LoadAllPoseLureEntitiesCfg()
}

func fixKeyPoseLureEntities(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPoseLureEntities)
}
func watchPoseLureEntitiesFunc(key string, js string) {
	mapPoseLureEntities := make(map[int64]*PoseLureEntities)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPoseLureEntities)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePoseLureEntities.Store(key, mapPoseLureEntities)
}

func GetAllPoseLureEntities(option ...consulconfig.Option) map[int64]*PoseLureEntities {
	fitKey := fixKeyPoseLureEntities(option...)
	store, ok := storePoseLureEntities.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseLureEntities)
		if ok {
			return storeMap
		}
	}
	lockPoseLureEntities.Lock()
	defer lockPoseLureEntities.Unlock()
	store, ok = storePoseLureEntities.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseLureEntities)
		if ok {
			return storeMap
		}
	}
	tblPoseLureEntities := make(map[int64]*PoseLureEntities)
	pose_lure_entities_str, err := consulconfig.GetInstance().GetConfig(strPoseLureEntities, option...)
	if pose_lure_entities_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_lure_entities_str), &tblPoseLureEntities)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_lure_entities", errUnmarshal)
		return nil
	}
	storePoseLureEntities.Store(fitKey, tblPoseLureEntities)
	return tblPoseLureEntities
}

func GetPoseLureEntities(id int64, option ...consulconfig.Option) *PoseLureEntities {
	fitKey := fixKeyPoseLureEntities(option...)
	store, ok := storePoseLureEntities.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseLureEntities)
		if ok {
			return storeMap[id]
		}
	}
	lockPoseLureEntities.Lock()
	defer lockPoseLureEntities.Unlock()
	store, ok = storePoseLureEntities.Load(fitKey)
	if ok {
		tblPoseLureEntities, ok := store.(*PoseLureEntities)
		if ok {
			return tblPoseLureEntities
		}
	}
	tblPoseLureEntities := make(map[int64]*PoseLureEntities)
	pose_lure_entities_str, err := consulconfig.GetInstance().GetConfig(strPoseLureEntities, option...)
	if pose_lure_entities_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_lure_entities_str), &tblPoseLureEntities)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_lure_entities", errUnmarshal)
		return nil
	}
	storePoseLureEntities.Store(fitKey, tblPoseLureEntities)
	return tblPoseLureEntities[id]
}

func LoadAllPoseLureEntitiesCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPoseLureEntities, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PoseLureEntities", successChannels)
	return nil
}

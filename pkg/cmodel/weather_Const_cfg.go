// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WeatherConst struct {
	Id                           int64 `json:"id"`
	WeatherChangeFrequencyConst  int64 `json:"weatherChangeFrequencyConst"`
	WeatherChangeTransitionConst bool  `json:"weatherChangeTransitionConst"`
}

var lockWeatherConst sync.RWMutex
var storeWeatherConst sync.Map
var strWeatherConst string = "weather_Const"

func InitWeatherConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWeatherConst, watchWeatherConstFunc)
	return LoadAllWeatherConstCfg()
}

func fixKeyWeatherConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWeatherConst)
}
func watchWeatherConstFunc(key string, js string) {
	store, ok := storeWeatherConst.Load(key)
	if !ok {
		store = &WeatherConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWeatherConst.Store(key, store)
}

func GetWeatherConst(option ...consulconfig.Option) *WeatherConst {
	fitKey := fixKeyWeatherConst(option...)
	store, ok := storeWeatherConst.Load(fitKey)
	if ok {
		tblWeatherConst, ok := store.(*WeatherConst)
		if ok {
			return tblWeatherConst
		}
	}
	lockWeatherConst.Lock()
	defer lockWeatherConst.Unlock()
	store, ok = storeWeatherConst.Load(fitKey)
	if ok {
		tblWeatherConst, ok := store.(*WeatherConst)
		if ok {
			return tblWeatherConst
		}
	}
	tblWeatherConst := &WeatherConst{}
	weather_Const_str, err := consulconfig.GetInstance().GetConfig(strWeatherConst, option...)
	if weather_Const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_Const_str), &tblWeatherConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strWeatherConst, errUnmarshal, weather_Const_str)
		return nil
	}
	storeWeatherConst.Store(fitKey, tblWeatherConst)
	return tblWeatherConst
}

func LoadAllWeatherConstCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strWeatherConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WeatherConst", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RechargeLimit struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	AgeType     int32  `json:"ageType"`
	SingleLimit int64  `json:"singleLimit"`
	MonthLimit  int64  `json:"monthLimit"`
}

var lockRechargeLimit sync.RWMutex
var storeRechargeLimit sync.Map
var strRechargeLimit string = "recharge_limit"

func InitRechargeLimitCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRechargeLimit, watchRechargeLimitFunc)
	return LoadAllRechargeLimitCfg()
}

func fixKeyRechargeLimit(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRechargeLimit)
}
func watchRechargeLimitFunc(key string, js string) {
	mapRechargeLimit := make(map[int64]*RechargeLimit)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRechargeLimit)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRechargeLimit.Store(key, mapRechargeLimit)
}

func GetAllRechargeLimit(option ...consulconfig.Option) map[int64]*RechargeLimit {
	fitKey := fixKeyRechargeLimit(option...)
	store, ok := storeRechargeLimit.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RechargeLimit)
		if ok {
			return storeMap
		}
	}
	lockRechargeLimit.Lock()
	defer lockRechargeLimit.Unlock()
	store, ok = storeRechargeLimit.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RechargeLimit)
		if ok {
			return storeMap
		}
	}
	tblRechargeLimit := make(map[int64]*RechargeLimit)
	recharge_limit_str, err := consulconfig.GetInstance().GetConfig(strRechargeLimit, option...)
	if recharge_limit_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(recharge_limit_str), &tblRechargeLimit)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "recharge_limit", errUnmarshal)
		return nil
	}
	storeRechargeLimit.Store(fitKey, tblRechargeLimit)
	return tblRechargeLimit
}

func GetRechargeLimit(id int64, option ...consulconfig.Option) *RechargeLimit {
	fitKey := fixKeyRechargeLimit(option...)
	store, ok := storeRechargeLimit.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RechargeLimit)
		if ok {
			return storeMap[id]
		}
	}
	lockRechargeLimit.Lock()
	defer lockRechargeLimit.Unlock()
	store, ok = storeRechargeLimit.Load(fitKey)
	if ok {
		tblRechargeLimit, ok := store.(*RechargeLimit)
		if ok {
			return tblRechargeLimit
		}
	}
	tblRechargeLimit := make(map[int64]*RechargeLimit)
	recharge_limit_str, err := consulconfig.GetInstance().GetConfig(strRechargeLimit, option...)
	if recharge_limit_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(recharge_limit_str), &tblRechargeLimit)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "recharge_limit", errUnmarshal)
		return nil
	}
	storeRechargeLimit.Store(fitKey, tblRechargeLimit)
	return tblRechargeLimit[id]
}

func LoadAllRechargeLimitCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRechargeLimit, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RechargeLimit", successChannels)
	return nil
}

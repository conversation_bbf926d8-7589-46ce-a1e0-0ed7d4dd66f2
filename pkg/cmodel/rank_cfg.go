// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Rank struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	Mark       string `json:"mark"`
	Language   int64  `json:"language"`
	Rule       int32  `json:"rule"`
	Open       bool   `json:"open"`
	FlushType  int32  `json:"flushType"`
	FlushTime  int32  `json:"flushTime"`
	RewardType int32  `json:"rewardType"`
	RewardTime int32  `json:"rewardTime"`
	RewardDesc int64  `json:"rewardDesc"`
}

var lockRank sync.RWMutex
var storeRank sync.Map
var strRank string = "rank"

func InitRankCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRank, watchRankFunc)
	return LoadAllRankCfg()
}

func fixKeyRank(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRank)
}
func watchRankFunc(key string, js string) {
	mapRank := make(map[int64]*Rank)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRank)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRank.Store(key, mapRank)
}

func GetAllRank(option ...consulconfig.Option) map[int64]*Rank {
	fitKey := fixKeyRank(option...)
	store, ok := storeRank.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Rank)
		if ok {
			return storeMap
		}
	}
	lockRank.Lock()
	defer lockRank.Unlock()
	store, ok = storeRank.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Rank)
		if ok {
			return storeMap
		}
	}
	tblRank := make(map[int64]*Rank)
	rank_str, err := consulconfig.GetInstance().GetConfig(strRank, option...)
	if rank_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rank_str), &tblRank)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rank", errUnmarshal)
		return nil
	}
	storeRank.Store(fitKey, tblRank)
	return tblRank
}

func GetRank(id int64, option ...consulconfig.Option) *Rank {
	fitKey := fixKeyRank(option...)
	store, ok := storeRank.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Rank)
		if ok {
			return storeMap[id]
		}
	}
	lockRank.Lock()
	defer lockRank.Unlock()
	store, ok = storeRank.Load(fitKey)
	if ok {
		tblRank, ok := store.(*Rank)
		if ok {
			return tblRank
		}
	}
	tblRank := make(map[int64]*Rank)
	rank_str, err := consulconfig.GetInstance().GetConfig(strRank, option...)
	if rank_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rank_str), &tblRank)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rank", errUnmarshal)
		return nil
	}
	storeRank.Store(fitKey, tblRank)
	return tblRank[id]
}

func LoadAllRankCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRank, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Rank", successChannels)
	return nil
}

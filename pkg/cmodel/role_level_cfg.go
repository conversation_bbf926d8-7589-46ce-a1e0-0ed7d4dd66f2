// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RoleLevelReward struct {
	ItemId    int64 `json:"itemId"`
	ItemCount int64 `json:"itemCount"`
}

type RoleLevel struct {
	Id               int64             `json:"id"`
	Name             string            `json:"name"`
	ExpNum           int64             `json:"expNum"`
	EnergyNum        int32             `json:"energyNum"`
	MaxEnergy        int32             `json:"maxEnergy"`
	EnergyRestoreSpd int32             `json:"energyRestoreSpd"`
	Reward           []RoleLevelReward `json:"reward"`
}

var lockRoleLevel sync.RWMutex
var storeRoleLevel sync.Map
var strRoleLevel string = "role_level"

func InitRoleLevelCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRoleLevel, watchRoleLevelFunc)
	return LoadAllRoleLevelCfg()
}

func fixKeyRoleLevel(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRoleLevel)
}
func watchRoleLevelFunc(key string, js string) {
	mapRoleLevel := make(map[int64]*RoleLevel)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRoleLevel)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRoleLevel.Store(key, mapRoleLevel)
}

func GetAllRoleLevel(option ...consulconfig.Option) map[int64]*RoleLevel {
	fitKey := fixKeyRoleLevel(option...)
	store, ok := storeRoleLevel.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleLevel)
		if ok {
			return storeMap
		}
	}
	lockRoleLevel.Lock()
	defer lockRoleLevel.Unlock()
	store, ok = storeRoleLevel.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleLevel)
		if ok {
			return storeMap
		}
	}
	tblRoleLevel := make(map[int64]*RoleLevel)
	role_level_str, err := consulconfig.GetInstance().GetConfig(strRoleLevel, option...)
	if role_level_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_level_str), &tblRoleLevel)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_level", errUnmarshal)
		return nil
	}
	storeRoleLevel.Store(fitKey, tblRoleLevel)
	return tblRoleLevel
}

func GetRoleLevel(id int64, option ...consulconfig.Option) *RoleLevel {
	fitKey := fixKeyRoleLevel(option...)
	store, ok := storeRoleLevel.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleLevel)
		if ok {
			return storeMap[id]
		}
	}
	lockRoleLevel.Lock()
	defer lockRoleLevel.Unlock()
	store, ok = storeRoleLevel.Load(fitKey)
	if ok {
		tblRoleLevel, ok := store.(*RoleLevel)
		if ok {
			return tblRoleLevel
		}
	}
	tblRoleLevel := make(map[int64]*RoleLevel)
	role_level_str, err := consulconfig.GetInstance().GetConfig(strRoleLevel, option...)
	if role_level_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_level_str), &tblRoleLevel)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_level", errUnmarshal)
		return nil
	}
	storeRoleLevel.Store(fitKey, tblRoleLevel)
	return tblRoleLevel[id]
}

func LoadAllRoleLevelCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRoleLevel, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RoleLevel", successChannels)
	return nil
}

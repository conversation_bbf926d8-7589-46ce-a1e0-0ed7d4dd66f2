// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FeatureHide struct {
	Id          int64  `json:"id"`
	FeatureName string `json:"featureName"`
	Mark        string `json:"mark"`
	HideType    int32  `json:"hideType"`
}

var lockFeatureHide sync.RWMutex
var storeFeatureHide sync.Map
var strFeatureHide string = "feature_hide"

func InitFeatureHideCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFeatureHide, watchFeatureHideFunc)
	return LoadAllFeatureHideCfg()
}

func fixKeyFeatureHide(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFeatureHide)
}
func watchFeatureHideFunc(key string, js string) {
	mapFeatureHide := make(map[int64]*FeatureHide)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFeatureHide)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFeatureHide.Store(key, mapFeatureHide)
}

func GetAllFeatureHide(option ...consulconfig.Option) map[int64]*FeatureHide {
	fitKey := fixKeyFeatureHide(option...)
	store, ok := storeFeatureHide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FeatureHide)
		if ok {
			return storeMap
		}
	}
	lockFeatureHide.Lock()
	defer lockFeatureHide.Unlock()
	store, ok = storeFeatureHide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FeatureHide)
		if ok {
			return storeMap
		}
	}
	tblFeatureHide := make(map[int64]*FeatureHide)
	feature_hide_str, err := consulconfig.GetInstance().GetConfig(strFeatureHide, option...)
	if feature_hide_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(feature_hide_str), &tblFeatureHide)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "feature_hide", errUnmarshal)
		return nil
	}
	storeFeatureHide.Store(fitKey, tblFeatureHide)
	return tblFeatureHide
}

func GetFeatureHide(id int64, option ...consulconfig.Option) *FeatureHide {
	fitKey := fixKeyFeatureHide(option...)
	store, ok := storeFeatureHide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FeatureHide)
		if ok {
			return storeMap[id]
		}
	}
	lockFeatureHide.Lock()
	defer lockFeatureHide.Unlock()
	store, ok = storeFeatureHide.Load(fitKey)
	if ok {
		tblFeatureHide, ok := store.(*FeatureHide)
		if ok {
			return tblFeatureHide
		}
	}
	tblFeatureHide := make(map[int64]*FeatureHide)
	feature_hide_str, err := consulconfig.GetInstance().GetConfig(strFeatureHide, option...)
	if feature_hide_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(feature_hide_str), &tblFeatureHide)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "feature_hide", errUnmarshal)
		return nil
	}
	storeFeatureHide.Store(fitKey, tblFeatureHide)
	return tblFeatureHide[id]
}

func LoadAllFeatureHideCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFeatureHide, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FeatureHide", successChannels)
	return nil
}

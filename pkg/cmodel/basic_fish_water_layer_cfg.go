// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishWaterLayer struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	LayerIndex  int32  `json:"layerIndex"`
	Description string `json:"description"`
}

var lockBasicFishWaterLayer sync.RWMutex
var storeBasicFishWaterLayer sync.Map
var strBasicFishWaterLayer string = "basic_fish_water_layer"

func InitBasicFishWaterLayerCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishWaterLayer, watchBasicFishWaterLayerFunc)
	return LoadAllBasicFishWaterLayerCfg()
}

func fixKeyBasicFishWaterLayer(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishWaterLayer)
}
func watchBasicFishWaterLayerFunc(key string, js string) {
	mapBasicFishWaterLayer := make(map[int64]*BasicFishWaterLayer)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishWaterLayer)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishWaterLayer.Store(key, mapBasicFishWaterLayer)
}

func GetAllBasicFishWaterLayer(option ...consulconfig.Option) map[int64]*BasicFishWaterLayer {
	fitKey := fixKeyBasicFishWaterLayer(option...)
	store, ok := storeBasicFishWaterLayer.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishWaterLayer)
		if ok {
			return storeMap
		}
	}
	lockBasicFishWaterLayer.Lock()
	defer lockBasicFishWaterLayer.Unlock()
	store, ok = storeBasicFishWaterLayer.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishWaterLayer)
		if ok {
			return storeMap
		}
	}
	tblBasicFishWaterLayer := make(map[int64]*BasicFishWaterLayer)
	basic_fish_water_layer_str, err := consulconfig.GetInstance().GetConfig(strBasicFishWaterLayer, option...)
	if basic_fish_water_layer_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_water_layer_str), &tblBasicFishWaterLayer)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_water_layer", errUnmarshal)
		return nil
	}
	storeBasicFishWaterLayer.Store(fitKey, tblBasicFishWaterLayer)
	return tblBasicFishWaterLayer
}

func GetBasicFishWaterLayer(id int64, option ...consulconfig.Option) *BasicFishWaterLayer {
	fitKey := fixKeyBasicFishWaterLayer(option...)
	store, ok := storeBasicFishWaterLayer.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishWaterLayer)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishWaterLayer.Lock()
	defer lockBasicFishWaterLayer.Unlock()
	store, ok = storeBasicFishWaterLayer.Load(fitKey)
	if ok {
		tblBasicFishWaterLayer, ok := store.(*BasicFishWaterLayer)
		if ok {
			return tblBasicFishWaterLayer
		}
	}
	tblBasicFishWaterLayer := make(map[int64]*BasicFishWaterLayer)
	basic_fish_water_layer_str, err := consulconfig.GetInstance().GetConfig(strBasicFishWaterLayer, option...)
	if basic_fish_water_layer_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_water_layer_str), &tblBasicFishWaterLayer)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_water_layer", errUnmarshal)
		return nil
	}
	storeBasicFishWaterLayer.Store(fitKey, tblBasicFishWaterLayer)
	return tblBasicFishWaterLayer[id]
}

func LoadAllBasicFishWaterLayerCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishWaterLayer, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishWaterLayer", successChannels)
	return nil
}

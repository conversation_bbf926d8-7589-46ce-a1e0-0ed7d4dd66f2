// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type EventTempMapDetails struct {
	Label   int32 `json:"label"`
	Operate int32 `json:"operate"`
	Value   int64 `json:"value"`
}

type EventTempMap struct {
	Id      int64                 `json:"id"`
	Name    string                `json:"name"`
	EventId int64                 `json:"eventId"`
	Open    bool                  `json:"open"`
	BcType  int64                 `json:"bcType"`
	NtfType int64                 `json:"ntfType"`
	TempId  int64                 `json:"tempId"`
	Details []EventTempMapDetails `json:"details"`
}

var lockEventTempMap sync.RWMutex
var storeEventTempMap sync.Map
var strEventTempMap string = "event_temp_map"

func InitEventTempMapCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strEventTempMap, watchEventTempMapFunc)
	return LoadAllEventTempMapCfg()
}

func fixKeyEventTempMap(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strEventTempMap)
}
func watchEventTempMapFunc(key string, js string) {
	mapEventTempMap := make(map[int64]*EventTempMap)
	errUnmarshal := json.Unmarshal([]byte(js), &mapEventTempMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeEventTempMap.Store(key, mapEventTempMap)
}

func GetAllEventTempMap(option ...consulconfig.Option) map[int64]*EventTempMap {
	fitKey := fixKeyEventTempMap(option...)
	store, ok := storeEventTempMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*EventTempMap)
		if ok {
			return storeMap
		}
	}
	lockEventTempMap.Lock()
	defer lockEventTempMap.Unlock()
	store, ok = storeEventTempMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*EventTempMap)
		if ok {
			return storeMap
		}
	}
	tblEventTempMap := make(map[int64]*EventTempMap)
	event_temp_map_str, err := consulconfig.GetInstance().GetConfig(strEventTempMap, option...)
	if event_temp_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(event_temp_map_str), &tblEventTempMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "event_temp_map", errUnmarshal)
		return nil
	}
	storeEventTempMap.Store(fitKey, tblEventTempMap)
	return tblEventTempMap
}

func GetEventTempMap(id int64, option ...consulconfig.Option) *EventTempMap {
	fitKey := fixKeyEventTempMap(option...)
	store, ok := storeEventTempMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*EventTempMap)
		if ok {
			return storeMap[id]
		}
	}
	lockEventTempMap.Lock()
	defer lockEventTempMap.Unlock()
	store, ok = storeEventTempMap.Load(fitKey)
	if ok {
		tblEventTempMap, ok := store.(*EventTempMap)
		if ok {
			return tblEventTempMap
		}
	}
	tblEventTempMap := make(map[int64]*EventTempMap)
	event_temp_map_str, err := consulconfig.GetInstance().GetConfig(strEventTempMap, option...)
	if event_temp_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(event_temp_map_str), &tblEventTempMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "event_temp_map", errUnmarshal)
		return nil
	}
	storeEventTempMap.Store(fitKey, tblEventTempMap)
	return tblEventTempMap[id]
}

func LoadAllEventTempMapCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strEventTempMap, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "EventTempMap", successChannels)
	return nil
}

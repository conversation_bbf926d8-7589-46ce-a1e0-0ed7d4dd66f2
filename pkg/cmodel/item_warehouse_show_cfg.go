// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ItemWarehouseShow struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	CategoryId  int32  `json:"categoryId"`
	ItemType    int32  `json:"itemType"`
	ItemSubType int32  `json:"itemSubType"`
	IsShow      bool   `json:"isShow"`
	Mark        string `json:"mark"`
}

var lockItemWarehouseShow sync.RWMutex
var storeItemWarehouseShow sync.Map
var strItemWarehouseShow string = "item_warehouse_show"

func InitItemWarehouseShowCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItemWarehouseShow, watchItemWarehouseShowFunc)
	return LoadAllItemWarehouseShowCfg()
}

func fixKeyItemWarehouseShow(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItemWarehouseShow)
}
func watchItemWarehouseShowFunc(key string, js string) {
	mapItemWarehouseShow := make(map[int64]*ItemWarehouseShow)
	errUnmarshal := json.Unmarshal([]byte(js), &mapItemWarehouseShow)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItemWarehouseShow.Store(key, mapItemWarehouseShow)
}

func GetAllItemWarehouseShow(option ...consulconfig.Option) map[int64]*ItemWarehouseShow {
	fitKey := fixKeyItemWarehouseShow(option...)
	store, ok := storeItemWarehouseShow.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemWarehouseShow)
		if ok {
			return storeMap
		}
	}
	lockItemWarehouseShow.Lock()
	defer lockItemWarehouseShow.Unlock()
	store, ok = storeItemWarehouseShow.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemWarehouseShow)
		if ok {
			return storeMap
		}
	}
	tblItemWarehouseShow := make(map[int64]*ItemWarehouseShow)
	item_warehouse_show_str, err := consulconfig.GetInstance().GetConfig(strItemWarehouseShow, option...)
	if item_warehouse_show_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_warehouse_show_str), &tblItemWarehouseShow)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_warehouse_show", errUnmarshal)
		return nil
	}
	storeItemWarehouseShow.Store(fitKey, tblItemWarehouseShow)
	return tblItemWarehouseShow
}

func GetItemWarehouseShow(id int64, option ...consulconfig.Option) *ItemWarehouseShow {
	fitKey := fixKeyItemWarehouseShow(option...)
	store, ok := storeItemWarehouseShow.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemWarehouseShow)
		if ok {
			return storeMap[id]
		}
	}
	lockItemWarehouseShow.Lock()
	defer lockItemWarehouseShow.Unlock()
	store, ok = storeItemWarehouseShow.Load(fitKey)
	if ok {
		tblItemWarehouseShow, ok := store.(*ItemWarehouseShow)
		if ok {
			return tblItemWarehouseShow
		}
	}
	tblItemWarehouseShow := make(map[int64]*ItemWarehouseShow)
	item_warehouse_show_str, err := consulconfig.GetInstance().GetConfig(strItemWarehouseShow, option...)
	if item_warehouse_show_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_warehouse_show_str), &tblItemWarehouseShow)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_warehouse_show", errUnmarshal)
		return nil
	}
	storeItemWarehouseShow.Store(fitKey, tblItemWarehouseShow)
	return tblItemWarehouseShow[id]
}

func LoadAllItemWarehouseShowCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItemWarehouseShow, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ItemWarehouseShow", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WeatherPondPeriods struct {
	Period int64 `json:"period"`
	Weight int64 `json:"weight"`
}

type WeatherPond struct {
	Id      int64                `json:"id"`
	Name    string               `json:"name"`
	Pond    int64                `json:"pond"`
	Season  string               `json:"season"`
	Periods []WeatherPondPeriods `json:"Periods"`
}

var lockWeatherPond sync.RWMutex
var storeWeatherPond sync.Map
var strWeatherPond string = "weather_pond"

func InitWeatherPondCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWeatherPond, watchWeatherPondFunc)
	return LoadAllWeatherPondCfg()
}

func fixKeyWeatherPond(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWeatherPond)
}
func watchWeatherPondFunc(key string, js string) {
	mapWeatherPond := make(map[int64]*WeatherPond)
	errUnmarshal := json.Unmarshal([]byte(js), &mapWeatherPond)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWeatherPond.Store(key, mapWeatherPond)
}

func GetAllWeatherPond(option ...consulconfig.Option) map[int64]*WeatherPond {
	fitKey := fixKeyWeatherPond(option...)
	store, ok := storeWeatherPond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherPond)
		if ok {
			return storeMap
		}
	}
	lockWeatherPond.Lock()
	defer lockWeatherPond.Unlock()
	store, ok = storeWeatherPond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherPond)
		if ok {
			return storeMap
		}
	}
	tblWeatherPond := make(map[int64]*WeatherPond)
	weather_pond_str, err := consulconfig.GetInstance().GetConfig(strWeatherPond, option...)
	if weather_pond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_pond_str), &tblWeatherPond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_pond", errUnmarshal)
		return nil
	}
	storeWeatherPond.Store(fitKey, tblWeatherPond)
	return tblWeatherPond
}

func GetWeatherPond(id int64, option ...consulconfig.Option) *WeatherPond {
	fitKey := fixKeyWeatherPond(option...)
	store, ok := storeWeatherPond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WeatherPond)
		if ok {
			return storeMap[id]
		}
	}
	lockWeatherPond.Lock()
	defer lockWeatherPond.Unlock()
	store, ok = storeWeatherPond.Load(fitKey)
	if ok {
		tblWeatherPond, ok := store.(*WeatherPond)
		if ok {
			return tblWeatherPond
		}
	}
	tblWeatherPond := make(map[int64]*WeatherPond)
	weather_pond_str, err := consulconfig.GetInstance().GetConfig(strWeatherPond, option...)
	if weather_pond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(weather_pond_str), &tblWeatherPond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "weather_pond", errUnmarshal)
		return nil
	}
	storeWeatherPond.Store(fitKey, tblWeatherPond)
	return tblWeatherPond[id]
}

func LoadAllWeatherPondCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strWeatherPond, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WeatherPond", successChannels)
	return nil
}

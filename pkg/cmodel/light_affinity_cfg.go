// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LightAffinity struct {
	Id             int64   `json:"id"`
	Name           string  `json:"name"`
	PreferLight    float64 `json:"preferLight"`
	LightTolerance float64 `json:"lightTolerance"`
	LightThreshold float64 `json:"lightThreshold"`
}

var lockLightAffinity sync.RWMutex
var storeLightAffinity sync.Map
var strLightAffinity string = "light_affinity"

func InitLightAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLightAffinity, watchLightAffinityFunc)
	return LoadAllLightAffinityCfg()
}

func fixKeyLightAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLightAffinity)
}
func watchLightAffinityFunc(key string, js string) {
	mapLightAffinity := make(map[int64]*LightAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLightAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLightAffinity.Store(key, mapLightAffinity)
}

func GetAllLightAffinity(option ...consulconfig.Option) map[int64]*LightAffinity {
	fitKey := fixKeyLightAffinity(option...)
	store, ok := storeLightAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LightAffinity)
		if ok {
			return storeMap
		}
	}
	lockLightAffinity.Lock()
	defer lockLightAffinity.Unlock()
	store, ok = storeLightAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LightAffinity)
		if ok {
			return storeMap
		}
	}
	tblLightAffinity := make(map[int64]*LightAffinity)
	light_affinity_str, err := consulconfig.GetInstance().GetConfig(strLightAffinity, option...)
	if light_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(light_affinity_str), &tblLightAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "light_affinity", errUnmarshal)
		return nil
	}
	storeLightAffinity.Store(fitKey, tblLightAffinity)
	return tblLightAffinity
}

func GetLightAffinity(id int64, option ...consulconfig.Option) *LightAffinity {
	fitKey := fixKeyLightAffinity(option...)
	store, ok := storeLightAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LightAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockLightAffinity.Lock()
	defer lockLightAffinity.Unlock()
	store, ok = storeLightAffinity.Load(fitKey)
	if ok {
		tblLightAffinity, ok := store.(*LightAffinity)
		if ok {
			return tblLightAffinity
		}
	}
	tblLightAffinity := make(map[int64]*LightAffinity)
	light_affinity_str, err := consulconfig.GetInstance().GetConfig(strLightAffinity, option...)
	if light_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(light_affinity_str), &tblLightAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "light_affinity", errUnmarshal)
		return nil
	}
	storeLightAffinity.Store(fitKey, tblLightAffinity)
	return tblLightAffinity[id]
}

func LoadAllLightAffinityCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLightAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LightAffinity", successChannels)
	return nil
}

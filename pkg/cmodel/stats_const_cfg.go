// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StatsConst struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockStatsConst sync.RWMutex
var storeStatsConst sync.Map
var strStatsConst string = "stats_const"

func InitStatsConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStatsConst, watchStatsConstFunc)
	return LoadAllStatsConstCfg()
}

func fixKeyStatsConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStatsConst)
}
func watchStatsConstFunc(key string, js string) {
	mapStatsConst := make(map[int64]*StatsConst)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStatsConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStatsConst.Store(key, mapStatsConst)
}

func GetAllStatsConst(option ...consulconfig.Option) map[int64]*StatsConst {
	fitKey := fixKeyStatsConst(option...)
	store, ok := storeStatsConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsConst)
		if ok {
			return storeMap
		}
	}
	lockStatsConst.Lock()
	defer lockStatsConst.Unlock()
	store, ok = storeStatsConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsConst)
		if ok {
			return storeMap
		}
	}
	tblStatsConst := make(map[int64]*StatsConst)
	stats_const_str, err := consulconfig.GetInstance().GetConfig(strStatsConst, option...)
	if stats_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_const_str), &tblStatsConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_const", errUnmarshal)
		return nil
	}
	storeStatsConst.Store(fitKey, tblStatsConst)
	return tblStatsConst
}

func GetStatsConst(id int64, option ...consulconfig.Option) *StatsConst {
	fitKey := fixKeyStatsConst(option...)
	store, ok := storeStatsConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsConst)
		if ok {
			return storeMap[id]
		}
	}
	lockStatsConst.Lock()
	defer lockStatsConst.Unlock()
	store, ok = storeStatsConst.Load(fitKey)
	if ok {
		tblStatsConst, ok := store.(*StatsConst)
		if ok {
			return tblStatsConst
		}
	}
	tblStatsConst := make(map[int64]*StatsConst)
	stats_const_str, err := consulconfig.GetInstance().GetConfig(strStatsConst, option...)
	if stats_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_const_str), &tblStatsConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_const", errUnmarshal)
		return nil
	}
	storeStatsConst.Store(fitKey, tblStatsConst)
	return tblStatsConst[id]
}

func LoadAllStatsConstCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStatsConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StatsConst", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RoleFishingInfo struct {
	Id  int64  `json:"id"`
	Art string `json:"art"`
}

var lockRoleFishingInfo sync.RWMutex
var storeRoleFishingInfo sync.Map
var strRoleFishingInfo string = "role_fishing_info"

func InitRoleFishingInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRoleFishingInfo, watchRoleFishingInfoFunc)
	return LoadAllRoleFishingInfoCfg()
}

func fixKeyRoleFishingInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRoleFishingInfo)
}
func watchRoleFishingInfoFunc(key string, js string) {
	mapRoleFishingInfo := make(map[int64]*RoleFishingInfo)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRoleFishingInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRoleFishingInfo.Store(key, mapRoleFishingInfo)
}

func GetAllRoleFishingInfo(option ...consulconfig.Option) map[int64]*RoleFishingInfo {
	fitKey := fixKeyRoleFishingInfo(option...)
	store, ok := storeRoleFishingInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleFishingInfo)
		if ok {
			return storeMap
		}
	}
	lockRoleFishingInfo.Lock()
	defer lockRoleFishingInfo.Unlock()
	store, ok = storeRoleFishingInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleFishingInfo)
		if ok {
			return storeMap
		}
	}
	tblRoleFishingInfo := make(map[int64]*RoleFishingInfo)
	role_fishing_info_str, err := consulconfig.GetInstance().GetConfig(strRoleFishingInfo, option...)
	if role_fishing_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_fishing_info_str), &tblRoleFishingInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_fishing_info", errUnmarshal)
		return nil
	}
	storeRoleFishingInfo.Store(fitKey, tblRoleFishingInfo)
	return tblRoleFishingInfo
}

func GetRoleFishingInfo(id int64, option ...consulconfig.Option) *RoleFishingInfo {
	fitKey := fixKeyRoleFishingInfo(option...)
	store, ok := storeRoleFishingInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleFishingInfo)
		if ok {
			return storeMap[id]
		}
	}
	lockRoleFishingInfo.Lock()
	defer lockRoleFishingInfo.Unlock()
	store, ok = storeRoleFishingInfo.Load(fitKey)
	if ok {
		tblRoleFishingInfo, ok := store.(*RoleFishingInfo)
		if ok {
			return tblRoleFishingInfo
		}
	}
	tblRoleFishingInfo := make(map[int64]*RoleFishingInfo)
	role_fishing_info_str, err := consulconfig.GetInstance().GetConfig(strRoleFishingInfo, option...)
	if role_fishing_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_fishing_info_str), &tblRoleFishingInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_fishing_info", errUnmarshal)
		return nil
	}
	storeRoleFishingInfo.Store(fitKey, tblRoleFishingInfo)
	return tblRoleFishingInfo[id]
}

func LoadAllRoleFishingInfoCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRoleFishingInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RoleFishingInfo", successChannels)
	return nil
}

// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageItemLangName struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageItemLangDes struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageItem struct {
	Id       int64                `json:"id"`
	Name     string               `json:"name"`
	LangName LanguageItemLangName `json:"lang_name"`
	LangDes  LanguageItemLangDes  `json:"lang_des"`
}

var lockLanguageItem sync.RWMutex
var storeLanguageItem sync.Map
var strLanguageItem string = "language_item"

func InitLanguageItemCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageItem, watchLanguageItemFunc)
	return LoadAllLanguageItemCfg()
}

func fixKeyLanguageItem(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageItem)
}
func watchLanguageItemFunc(key string, js string) {
	mapLanguageItem := make(map[int64]*LanguageItem)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageItem)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageItem.Store(key, mapLanguageItem)
}

func GetAllLanguageItem(option ...consulconfig.Option) map[int64]*LanguageItem {
	fitKey := fixKeyLanguageItem(option...)
	store, ok := storeLanguageItem.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageItem)
		if ok {
			return storeMap
		}
	}
	lockLanguageItem.Lock()
	defer lockLanguageItem.Unlock()
	store, ok = storeLanguageItem.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageItem)
		if ok {
			return storeMap
		}
	}
	tblLanguageItem := make(map[int64]*LanguageItem)
	language_item_str, err := consulconfig.GetInstance().GetConfig(strLanguageItem, option...)
	if language_item_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_item_str), &tblLanguageItem)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_item", errUnmarshal)
		return nil
	}
	storeLanguageItem.Store(fitKey, tblLanguageItem)
	return tblLanguageItem
}

func GetLanguageItem(id int64, option ...consulconfig.Option) *LanguageItem {
	fitKey := fixKeyLanguageItem(option...)
	store, ok := storeLanguageItem.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageItem)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageItem.Lock()
	defer lockLanguageItem.Unlock()
	store, ok = storeLanguageItem.Load(fitKey)
	if ok {
		tblLanguageItem, ok := store.(*LanguageItem)
		if ok {
			return tblLanguageItem
		}
	}
	tblLanguageItem := make(map[int64]*LanguageItem)
	language_item_str, err := consulconfig.GetInstance().GetConfig(strLanguageItem, option...)
	if language_item_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_item_str), &tblLanguageItem)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_item", errUnmarshal)
		return nil
	}
	storeLanguageItem.Store(fitKey, tblLanguageItem)
	return tblLanguageItem[id]
}

func LoadAllLanguageItemCfg() error {
	channels := consulconfig.GetAllChannelMap()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageItem, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageItem", successChannels)
	return nil
}

// gm 模块

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: gm.proto

package gmPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// gm操作类型
type GM_TASK_OPERATE int32

const (
	GM_TASK_OPERATE_GM_TASK_UNKNOW         GM_TASK_OPERATE = 0
	GM_TASK_OPERATE_GM_TASK_OPERATE_SET    GM_TASK_OPERATE = 1 // 强制添加或刷新
	GM_TASK_OPERATE_GM_TASK_OPERATE_FINISH GM_TASK_OPERATE = 2 // 强制完成
	GM_TASK_OPERATE_GM_TASK_OPERATE_DELETE GM_TASK_OPERATE = 3 // 强制删除
)

// Enum value maps for GM_TASK_OPERATE.
var (
	GM_TASK_OPERATE_name = map[int32]string{
		0: "GM_TASK_UNKNOW",
		1: "GM_TASK_OPERATE_SET",
		2: "GM_TASK_OPERATE_FINISH",
		3: "GM_TASK_OPERATE_DELETE",
	}
	GM_TASK_OPERATE_value = map[string]int32{
		"GM_TASK_UNKNOW":         0,
		"GM_TASK_OPERATE_SET":    1,
		"GM_TASK_OPERATE_FINISH": 2,
		"GM_TASK_OPERATE_DELETE": 3,
	}
)

func (x GM_TASK_OPERATE) Enum() *GM_TASK_OPERATE {
	p := new(GM_TASK_OPERATE)
	*p = x
	return p
}

func (x GM_TASK_OPERATE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GM_TASK_OPERATE) Descriptor() protoreflect.EnumDescriptor {
	return file_gm_proto_enumTypes[0].Descriptor()
}

func (GM_TASK_OPERATE) Type() protoreflect.EnumType {
	return &file_gm_proto_enumTypes[0]
}

func (x GM_TASK_OPERATE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GM_TASK_OPERATE.Descriptor instead.
func (GM_TASK_OPERATE) EnumDescriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{0}
}

type GmOperateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid    common.PRODUCT_ID   `protobuf:"varint,1,opt,name=pid,proto3,enum=common.PRODUCT_ID" json:"pid,omitempty"`   // product Id
	Sercet string              `protobuf:"bytes,2,opt,name=sercet,proto3" json:"sercet,omitempty"`                     // 加密密钥
	Cmd    common.GM_CMD       `protobuf:"varint,3,opt,name=cmd,proto3,enum=common.GM_CMD" json:"cmd,omitempty"`       // 操作类型
	Data   string              `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`                         // 操作请求 Json打包
	Reason string              `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`                     // 操作原因
	User   string              `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`                         // 操作者
	Cid    common.CHANNEL_TYPE `protobuf:"varint,7,opt,name=cid,proto3,enum=common.CHANNEL_TYPE" json:"cid,omitempty"` // 渠道id
}

func (x *GmOperateReq) Reset() {
	*x = GmOperateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmOperateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmOperateReq) ProtoMessage() {}

func (x *GmOperateReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmOperateReq.ProtoReflect.Descriptor instead.
func (*GmOperateReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{0}
}

func (x *GmOperateReq) GetPid() common.PRODUCT_ID {
	if x != nil {
		return x.Pid
	}
	return common.PRODUCT_ID(0)
}

func (x *GmOperateReq) GetSercet() string {
	if x != nil {
		return x.Sercet
	}
	return ""
}

func (x *GmOperateReq) GetCmd() common.GM_CMD {
	if x != nil {
		return x.Cmd
	}
	return common.GM_CMD(0)
}

func (x *GmOperateReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *GmOperateReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GmOperateReq) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *GmOperateReq) GetCid() common.CHANNEL_TYPE {
	if x != nil {
		return x.Cid
	}
	return common.CHANNEL_TYPE(0)
}

type GmOperateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Oid  string         `protobuf:"bytes,2,opt,name=oid,proto3" json:"oid,omitempty"`   // order_id 操作唯一id
	Data string         `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"` // 操作结果 Json打包
}

func (x *GmOperateRsp) Reset() {
	*x = GmOperateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmOperateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmOperateRsp) ProtoMessage() {}

func (x *GmOperateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmOperateRsp.ProtoReflect.Descriptor instead.
func (*GmOperateRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{1}
}

func (x *GmOperateRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GmOperateRsp) GetOid() string {
	if x != nil {
		return x.Oid
	}
	return ""
}

func (x *GmOperateRsp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// ------------------------------------
//
//	world
//
// ------------------------------------
type GmCmdGameTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts     int64 `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`         // 设置时间戳
	Offset int64 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"` // 设置偏移值(秒) 优先
}

func (x *GmCmdGameTimeReq) Reset() {
	*x = GmCmdGameTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdGameTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdGameTimeReq) ProtoMessage() {}

func (x *GmCmdGameTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdGameTimeReq.ProtoReflect.Descriptor instead.
func (*GmCmdGameTimeReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{2}
}

func (x *GmCmdGameTimeReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *GmCmdGameTimeReq) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type GmCmdGameTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdGameTimeRsp) Reset() {
	*x = GmCmdGameTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdGameTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdGameTimeRsp) ProtoMessage() {}

func (x *GmCmdGameTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdGameTimeRsp.ProtoReflect.Descriptor instead.
func (*GmCmdGameTimeRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{3}
}

type GmCmdClearWeatherReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId int64 `protobuf:"varint,1,opt,name=pond_id,proto3" json:"pond_id,omitempty"`
}

func (x *GmCmdClearWeatherReq) Reset() {
	*x = GmCmdClearWeatherReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdClearWeatherReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdClearWeatherReq) ProtoMessage() {}

func (x *GmCmdClearWeatherReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdClearWeatherReq.ProtoReflect.Descriptor instead.
func (*GmCmdClearWeatherReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{4}
}

func (x *GmCmdClearWeatherReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

type GmCmdClearWeatherRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdClearWeatherRsp) Reset() {
	*x = GmCmdClearWeatherRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdClearWeatherRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdClearWeatherRsp) ProtoMessage() {}

func (x *GmCmdClearWeatherRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdClearWeatherRsp.ProtoReflect.Descriptor instead.
func (*GmCmdClearWeatherRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{5}
}

// ------------------------------------
//
//	hallsrv
//
// ------------------------------------
// 道具修改请求
type GmCmdOperateItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId     int32                 `protobuf:"varint,1,opt,name=product_id,json=ProductId,proto3" json:"product_id,omitempty"`                                        // 产品 ID
	PlayerId      uint64                `protobuf:"varint,2,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"`                                           // 玩家id
	ItemOperation common.ITEM_OPERATION `protobuf:"varint,3,opt,name=item_operation,json=ItemOperation,proto3,enum=common.ITEM_OPERATION" json:"item_operation,omitempty"` // 物品操作类型
	ItemId        int64                 `protobuf:"varint,4,opt,name=item_id,json=ItemId,proto3" json:"item_id,omitempty"`                                                 // 物品ID
	ItemCount     int64                 `protobuf:"varint,5,opt,name=item_count,json=ItemCount,proto3" json:"item_count,omitempty"`                                        // 物品数量
	IsUnpack      bool                  `protobuf:"varint,6,opt,name=is_unpack,json=IsUnpack,proto3" json:"is_unpack,omitempty"`                                           // 是否拆包
}

func (x *GmCmdOperateItemReq) Reset() {
	*x = GmCmdOperateItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdOperateItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdOperateItemReq) ProtoMessage() {}

func (x *GmCmdOperateItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdOperateItemReq.ProtoReflect.Descriptor instead.
func (*GmCmdOperateItemReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{6}
}

func (x *GmCmdOperateItemReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GmCmdOperateItemReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdOperateItemReq) GetItemOperation() common.ITEM_OPERATION {
	if x != nil {
		return x.ItemOperation
	}
	return common.ITEM_OPERATION(0)
}

func (x *GmCmdOperateItemReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *GmCmdOperateItemReq) GetItemCount() int64 {
	if x != nil {
		return x.ItemCount
	}
	return 0
}

func (x *GmCmdOperateItemReq) GetIsUnpack() bool {
	if x != nil {
		return x.IsUnpack
	}
	return false
}

// 道具修改请求
type GmCmdOperateItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardInfo *common.Reward `protobuf:"bytes,1,opt,name=reward_info,json=RewardInfo,proto3" json:"reward_info,omitempty"` // 物品信息
}

func (x *GmCmdOperateItemRsp) Reset() {
	*x = GmCmdOperateItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdOperateItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdOperateItemRsp) ProtoMessage() {}

func (x *GmCmdOperateItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdOperateItemRsp.ProtoReflect.Descriptor instead.
func (*GmCmdOperateItemRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{7}
}

func (x *GmCmdOperateItemRsp) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

// 清理背包
type GmCmdClearCatogoryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32                `protobuf:"varint,1,opt,name=product_id,json=ProductId,proto3" json:"product_id,omitempty"`                      // 产品 ID
	PlayerId  uint64               `protobuf:"varint,2,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"`                         // 玩家id
	Category  common.ITEM_CATEGORY `protobuf:"varint,3,opt,name=category,json=Category,proto3,enum=common.ITEM_CATEGORY" json:"category,omitempty"` // 大分类
}

func (x *GmCmdClearCatogoryReq) Reset() {
	*x = GmCmdClearCatogoryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdClearCatogoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdClearCatogoryReq) ProtoMessage() {}

func (x *GmCmdClearCatogoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdClearCatogoryReq.ProtoReflect.Descriptor instead.
func (*GmCmdClearCatogoryReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{8}
}

func (x *GmCmdClearCatogoryReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GmCmdClearCatogoryReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdClearCatogoryReq) GetCategory() common.ITEM_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.ITEM_CATEGORY(0)
}

type GmCmdClearCatogoryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdClearCatogoryRsp) Reset() {
	*x = GmCmdClearCatogoryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdClearCatogoryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdClearCatogoryRsp) ProtoMessage() {}

func (x *GmCmdClearCatogoryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdClearCatogoryRsp.ProtoReflect.Descriptor instead.
func (*GmCmdClearCatogoryRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{9}
}

// GmCmdLoadRigRuleReq 加载干架规则
type GmCmdLoadRigRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32 `protobuf:"varint,1,opt,name=product_id,json=ProductId,proto3" json:"product_id,omitempty"` // 产品 ID
}

func (x *GmCmdLoadRigRuleReq) Reset() {
	*x = GmCmdLoadRigRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdLoadRigRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdLoadRigRuleReq) ProtoMessage() {}

func (x *GmCmdLoadRigRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdLoadRigRuleReq.ProtoReflect.Descriptor instead.
func (*GmCmdLoadRigRuleReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{10}
}

func (x *GmCmdLoadRigRuleReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

type GmCmdLoadRigRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdLoadRigRuleRsp) Reset() {
	*x = GmCmdLoadRigRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdLoadRigRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdLoadRigRuleRsp) ProtoMessage() {}

func (x *GmCmdLoadRigRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdLoadRigRuleRsp.ProtoReflect.Descriptor instead.
func (*GmCmdLoadRigRuleRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{11}
}

// 连续登录奖励
type GmCmdContinuousLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId   uint64 `protobuf:"varint,1,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"`
	LastUpdate int64  `protobuf:"varint,2,opt,name=last_update,json=LastUpdate,proto3" json:"last_update,omitempty"` // 强制修改上次领奖时间 （unixtime）
	IsClear    int32  `protobuf:"varint,3,opt,name=is_clear,json=IsClear,proto3" json:"is_clear,omitempty"`          // 是否清理数据 （优先）
}

func (x *GmCmdContinuousLoginReq) Reset() {
	*x = GmCmdContinuousLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdContinuousLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdContinuousLoginReq) ProtoMessage() {}

func (x *GmCmdContinuousLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdContinuousLoginReq.ProtoReflect.Descriptor instead.
func (*GmCmdContinuousLoginReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{12}
}

func (x *GmCmdContinuousLoginReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdContinuousLoginReq) GetLastUpdate() int64 {
	if x != nil {
		return x.LastUpdate
	}
	return 0
}

func (x *GmCmdContinuousLoginReq) GetIsClear() int32 {
	if x != nil {
		return x.IsClear
	}
	return 0
}

type GmCmdContinuousLoginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,json=Ret,proto3" json:"ret,omitempty"`
}

func (x *GmCmdContinuousLoginRsp) Reset() {
	*x = GmCmdContinuousLoginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdContinuousLoginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdContinuousLoginRsp) ProtoMessage() {}

func (x *GmCmdContinuousLoginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdContinuousLoginRsp.ProtoReflect.Descriptor instead.
func (*GmCmdContinuousLoginRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{13}
}

func (x *GmCmdContinuousLoginRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 创建CDK批次请求
type GmCmdCreateCDKReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelId        int32                        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`                                                        // 渠道ID (必填)
	Description      string                       `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                                                                      // 批次描述 (可选)
	GenerationOption common.CDK_GENERATION_OPTION `protobuf:"varint,3,opt,name=generation_option,json=generationOption,proto3,enum=common.CDK_GENERATION_OPTION" json:"generation_option,omitempty"` // CDK生成方式 (必填)
	GenerationCount  int32                        `protobuf:"varint,4,opt,name=generation_count,json=generationCount,proto3" json:"generation_count,omitempty"`                                      // 随机生成时，需要生成的CDK数量 (当 generation_option 为 "random" 时必填, >0)
	ManualCdks       []string                     `protobuf:"bytes,5,rep,name=manual_cdks,json=manualCdks,proto3" json:"manual_cdks,omitempty"`                                                      // 手动输入时，指定的CDK码列表 (当 generation_option 为 "manual" 时必填, 不能为空数组)
	StartTime        int64                        `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                                                        // 生效开始时间 (Unix时间戳, 秒) (必填)
	EndTime          int64                        `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                                                              // 生效结束时间 (Unix时间戳, 秒) (必填, 必须大于 start_time)
	CdkUseLimit      int32                        `protobuf:"varint,8,opt,name=cdk_use_limit,json=cdkUseLimit,proto3" json:"cdk_use_limit,omitempty"`                                                // 单个CDK可被所有用户使用的总次数 (-1表示不限制，默认为0)
	Rewards          []*common.ItemBase           `protobuf:"bytes,9,rep,name=rewards,proto3" json:"rewards,omitempty"`                                                                              // CDK奖励列表 (必填, 不能为空数组)
}

func (x *GmCmdCreateCDKReq) Reset() {
	*x = GmCmdCreateCDKReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdCreateCDKReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdCreateCDKReq) ProtoMessage() {}

func (x *GmCmdCreateCDKReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdCreateCDKReq.ProtoReflect.Descriptor instead.
func (*GmCmdCreateCDKReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{14}
}

func (x *GmCmdCreateCDKReq) GetChannelId() int32 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *GmCmdCreateCDKReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GmCmdCreateCDKReq) GetGenerationOption() common.CDK_GENERATION_OPTION {
	if x != nil {
		return x.GenerationOption
	}
	return common.CDK_GENERATION_OPTION(0)
}

func (x *GmCmdCreateCDKReq) GetGenerationCount() int32 {
	if x != nil {
		return x.GenerationCount
	}
	return 0
}

func (x *GmCmdCreateCDKReq) GetManualCdks() []string {
	if x != nil {
		return x.ManualCdks
	}
	return nil
}

func (x *GmCmdCreateCDKReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GmCmdCreateCDKReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GmCmdCreateCDKReq) GetCdkUseLimit() int32 {
	if x != nil {
		return x.CdkUseLimit
	}
	return 0
}

func (x *GmCmdCreateCDKReq) GetRewards() []*common.ItemBase {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 创建CDK批次响应
type GmCmdCreateCDKRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *GmCmdCreateCDKRsp) Reset() {
	*x = GmCmdCreateCDKRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdCreateCDKRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdCreateCDKRsp) ProtoMessage() {}

func (x *GmCmdCreateCDKRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdCreateCDKRsp.ProtoReflect.Descriptor instead.
func (*GmCmdCreateCDKRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{15}
}

func (x *GmCmdCreateCDKRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// CDK批次查询请求
type GmCmdQueryCDKBatchesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *common.PaginationReq   `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`                       // 分页信息
	Status     common.CDK_BATCH_STATUS `protobuf:"varint,2,opt,name=status,proto3,enum=common.CDK_BATCH_STATUS" json:"status,omitempty"` // 状态筛选，可选
}

func (x *GmCmdQueryCDKBatchesReq) Reset() {
	*x = GmCmdQueryCDKBatchesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdQueryCDKBatchesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdQueryCDKBatchesReq) ProtoMessage() {}

func (x *GmCmdQueryCDKBatchesReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdQueryCDKBatchesReq.ProtoReflect.Descriptor instead.
func (*GmCmdQueryCDKBatchesReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{16}
}

func (x *GmCmdQueryCDKBatchesReq) GetPagination() *common.PaginationReq {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GmCmdQueryCDKBatchesReq) GetStatus() common.CDK_BATCH_STATUS {
	if x != nil {
		return x.Status
	}
	return common.CDK_BATCH_STATUS(0)
}

// CDK批次摘要信息
type CDKBatchSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                         // 批次ID
	ChannelId   common.CHANNEL_TYPE     `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3,enum=common.CHANNEL_TYPE" json:"channel_id,omitempty"` // 渠道ID
	Description string                  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                                        // 批次描述
	CdkCount    int32                   `protobuf:"varint,4,opt,name=cdk_count,json=cdkCount,proto3" json:"cdk_count,omitempty"`                             // CDK数量
	Status      common.CDK_BATCH_STATUS `protobuf:"varint,5,opt,name=status,proto3,enum=common.CDK_BATCH_STATUS" json:"status,omitempty"`                    // 状态
	CreatedAt   int64                   `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                          // 创建时间 (Unix时间戳)
	UpdatedAt   int64                   `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                          // 更新时间 (Unix时间戳)
}

func (x *CDKBatchSummary) Reset() {
	*x = CDKBatchSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDKBatchSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDKBatchSummary) ProtoMessage() {}

func (x *CDKBatchSummary) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDKBatchSummary.ProtoReflect.Descriptor instead.
func (*CDKBatchSummary) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{17}
}

func (x *CDKBatchSummary) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CDKBatchSummary) GetChannelId() common.CHANNEL_TYPE {
	if x != nil {
		return x.ChannelId
	}
	return common.CHANNEL_TYPE(0)
}

func (x *CDKBatchSummary) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CDKBatchSummary) GetCdkCount() int32 {
	if x != nil {
		return x.CdkCount
	}
	return 0
}

func (x *CDKBatchSummary) GetStatus() common.CDK_BATCH_STATUS {
	if x != nil {
		return x.Status
	}
	return common.CDK_BATCH_STATUS(0)
}

func (x *CDKBatchSummary) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *CDKBatchSummary) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// CDK批次查询响应
type GmCmdQueryCDKBatchesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result        `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`               // 返回结果
	Total      int64                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`          // 总数
	Pagination *common.PaginationReq `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"` // 分页信息
	Batches    []*CDKBatchSummary    `protobuf:"bytes,4,rep,name=batches,proto3" json:"batches,omitempty"`       // 批次列表
}

func (x *GmCmdQueryCDKBatchesRsp) Reset() {
	*x = GmCmdQueryCDKBatchesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdQueryCDKBatchesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdQueryCDKBatchesRsp) ProtoMessage() {}

func (x *GmCmdQueryCDKBatchesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdQueryCDKBatchesRsp.ProtoReflect.Descriptor instead.
func (*GmCmdQueryCDKBatchesRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{18}
}

func (x *GmCmdQueryCDKBatchesRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GmCmdQueryCDKBatchesRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GmCmdQueryCDKBatchesRsp) GetPagination() *common.PaginationReq {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GmCmdQueryCDKBatchesRsp) GetBatches() []*CDKBatchSummary {
	if x != nil {
		return x.Batches
	}
	return nil
}

// CDK记录查询请求
type GmCmdQueryCDKRecordsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId  uint64 `protobuf:"varint,1,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`    // 批次ID
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码，从1开始
	PageSize int32  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GmCmdQueryCDKRecordsReq) Reset() {
	*x = GmCmdQueryCDKRecordsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdQueryCDKRecordsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdQueryCDKRecordsReq) ProtoMessage() {}

func (x *GmCmdQueryCDKRecordsReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdQueryCDKRecordsReq.ProtoReflect.Descriptor instead.
func (*GmCmdQueryCDKRecordsReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{19}
}

func (x *GmCmdQueryCDKRecordsReq) GetBatchId() uint64 {
	if x != nil {
		return x.BatchId
	}
	return 0
}

func (x *GmCmdQueryCDKRecordsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GmCmdQueryCDKRecordsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// CDK记录信息
type CDKRecordInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                // 记录ID
	Cdk       string `protobuf:"bytes,2,opt,name=cdk,proto3" json:"cdk,omitempty"`                               // CDK码
	UsedCount int32  `protobuf:"varint,3,opt,name=used_count,json=usedCount,proto3" json:"used_count,omitempty"` // 使用次数
	CreatedAt int64  `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间 (Unix时间戳)
	UpdatedAt int64  `protobuf:"varint,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间 (Unix时间戳)
}

func (x *CDKRecordInfo) Reset() {
	*x = CDKRecordInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDKRecordInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDKRecordInfo) ProtoMessage() {}

func (x *CDKRecordInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDKRecordInfo.ProtoReflect.Descriptor instead.
func (*CDKRecordInfo) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{20}
}

func (x *CDKRecordInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CDKRecordInfo) GetCdk() string {
	if x != nil {
		return x.Cdk
	}
	return ""
}

func (x *CDKRecordInfo) GetUsedCount() int32 {
	if x != nil {
		return x.UsedCount
	}
	return 0
}

func (x *CDKRecordInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *CDKRecordInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// CDK记录查询响应
type GmCmdQueryCDKRecordsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result        `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`               // 返回结果
	Total      int64                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`          // 总数
	Pagination *common.PaginationReq `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"` // 分页信息
	Records    []*CDKRecordInfo      `protobuf:"bytes,4,rep,name=records,proto3" json:"records,omitempty"`       // CDK记录列表
}

func (x *GmCmdQueryCDKRecordsRsp) Reset() {
	*x = GmCmdQueryCDKRecordsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdQueryCDKRecordsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdQueryCDKRecordsRsp) ProtoMessage() {}

func (x *GmCmdQueryCDKRecordsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdQueryCDKRecordsRsp.ProtoReflect.Descriptor instead.
func (*GmCmdQueryCDKRecordsRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{21}
}

func (x *GmCmdQueryCDKRecordsRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GmCmdQueryCDKRecordsRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GmCmdQueryCDKRecordsRsp) GetPagination() *common.PaginationReq {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GmCmdQueryCDKRecordsRsp) GetRecords() []*CDKRecordInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

// CDK批次作废请求
type GmCmdDisableCDKBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId    uint64 `protobuf:"varint,1,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`          // 批次ID
	OperatorId uint64 `protobuf:"varint,2,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"` // 操作人ID，可选
}

func (x *GmCmdDisableCDKBatchReq) Reset() {
	*x = GmCmdDisableCDKBatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdDisableCDKBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdDisableCDKBatchReq) ProtoMessage() {}

func (x *GmCmdDisableCDKBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdDisableCDKBatchReq.ProtoReflect.Descriptor instead.
func (*GmCmdDisableCDKBatchReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{22}
}

func (x *GmCmdDisableCDKBatchReq) GetBatchId() uint64 {
	if x != nil {
		return x.BatchId
	}
	return 0
}

func (x *GmCmdDisableCDKBatchReq) GetOperatorId() uint64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

// CDK批次作废响应
type GmCmdDisableCDKBatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *GmCmdDisableCDKBatchRsp) Reset() {
	*x = GmCmdDisableCDKBatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdDisableCDKBatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdDisableCDKBatchRsp) ProtoMessage() {}

func (x *GmCmdDisableCDKBatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdDisableCDKBatchRsp.ProtoReflect.Descriptor instead.
func (*GmCmdDisableCDKBatchRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{23}
}

func (x *GmCmdDisableCDKBatchRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// gm强制操作任务
type GmCmdOperateTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64          `protobuf:"varint,1,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"` // 玩家id
	TaskId   int64           `protobuf:"varint,2,opt,name=task_id,json=TaskId,proto3" json:"task_id,omitempty"`
	Operate  GM_TASK_OPERATE `protobuf:"varint,3,opt,name=operate,json=Operate,proto3,enum=gmPB.GM_TASK_OPERATE" json:"operate,omitempty"`
}

func (x *GmCmdOperateTaskReq) Reset() {
	*x = GmCmdOperateTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdOperateTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdOperateTaskReq) ProtoMessage() {}

func (x *GmCmdOperateTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdOperateTaskReq.ProtoReflect.Descriptor instead.
func (*GmCmdOperateTaskReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{24}
}

func (x *GmCmdOperateTaskReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdOperateTaskReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GmCmdOperateTaskReq) GetOperate() GM_TASK_OPERATE {
	if x != nil {
		return x.Operate
	}
	return GM_TASK_OPERATE_GM_TASK_UNKNOW
}

type GmCmdOperateTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdOperateTaskRsp) Reset() {
	*x = GmCmdOperateTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdOperateTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdOperateTaskRsp) ProtoMessage() {}

func (x *GmCmdOperateTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdOperateTaskRsp.ProtoReflect.Descriptor instead.
func (*GmCmdOperateTaskRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{25}
}

// 设置积分进度
type GmCmdTaskProgressSetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"`
	Category int64  `protobuf:"varint,2,opt,name=category,json=Category,proto3" json:"category,omitempty"`
	SubId    int64  `protobuf:"varint,3,opt,name=sub_id,json=SubId,proto3" json:"sub_id,omitempty"`
	Score    int64  `protobuf:"varint,4,opt,name=score,json=Score,proto3" json:"score,omitempty"`
}

func (x *GmCmdTaskProgressSetReq) Reset() {
	*x = GmCmdTaskProgressSetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdTaskProgressSetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdTaskProgressSetReq) ProtoMessage() {}

func (x *GmCmdTaskProgressSetReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdTaskProgressSetReq.ProtoReflect.Descriptor instead.
func (*GmCmdTaskProgressSetReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{26}
}

func (x *GmCmdTaskProgressSetReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdTaskProgressSetReq) GetCategory() int64 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *GmCmdTaskProgressSetReq) GetSubId() int64 {
	if x != nil {
		return x.SubId
	}
	return 0
}

func (x *GmCmdTaskProgressSetReq) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type GmCmdTaskProgressSetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,json=Ret,proto3" json:"ret,omitempty"`
}

func (x *GmCmdTaskProgressSetRsp) Reset() {
	*x = GmCmdTaskProgressSetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdTaskProgressSetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdTaskProgressSetRsp) ProtoMessage() {}

func (x *GmCmdTaskProgressSetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdTaskProgressSetRsp.ProtoReflect.Descriptor instead.
func (*GmCmdTaskProgressSetRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{27}
}

func (x *GmCmdTaskProgressSetRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 更新子任务进度
type GmCmdTaskSubUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64 `protobuf:"varint,1,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"`
	TaskId    int64  `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	SubTaskId int64  `protobuf:"varint,3,opt,name=sub_task_id,json=subTaskId,proto3" json:"sub_task_id,omitempty"`
	Progress  int64  `protobuf:"varint,4,opt,name=progress,proto3" json:"progress,omitempty"`
}

func (x *GmCmdTaskSubUpdateReq) Reset() {
	*x = GmCmdTaskSubUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdTaskSubUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdTaskSubUpdateReq) ProtoMessage() {}

func (x *GmCmdTaskSubUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdTaskSubUpdateReq.ProtoReflect.Descriptor instead.
func (*GmCmdTaskSubUpdateReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{28}
}

func (x *GmCmdTaskSubUpdateReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdTaskSubUpdateReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GmCmdTaskSubUpdateReq) GetSubTaskId() int64 {
	if x != nil {
		return x.SubTaskId
	}
	return 0
}

func (x *GmCmdTaskSubUpdateReq) GetProgress() int64 {
	if x != nil {
		return x.Progress
	}
	return 0
}

type GmCmdTaskSubUpdateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdTaskSubUpdateRsp) Reset() {
	*x = GmCmdTaskSubUpdateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdTaskSubUpdateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdTaskSubUpdateRsp) ProtoMessage() {}

func (x *GmCmdTaskSubUpdateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdTaskSubUpdateRsp.ProtoReflect.Descriptor instead.
func (*GmCmdTaskSubUpdateRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{29}
}

// ------------------------------------
//
//	spot
//
// ------------------------------------
// 修改体力 针对在钓场中的玩家
type GmCmdModifyEnergyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64 `protobuf:"varint,1,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"`    // 玩家id
	EnergyNum int32  `protobuf:"varint,2,opt,name=energy_num,json=EnergyNum,proto3" json:"energy_num,omitempty"` // 修改后的体力值数量
}

func (x *GmCmdModifyEnergyReq) Reset() {
	*x = GmCmdModifyEnergyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdModifyEnergyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdModifyEnergyReq) ProtoMessage() {}

func (x *GmCmdModifyEnergyReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdModifyEnergyReq.ProtoReflect.Descriptor instead.
func (*GmCmdModifyEnergyReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{30}
}

func (x *GmCmdModifyEnergyReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdModifyEnergyReq) GetEnergyNum() int32 {
	if x != nil {
		return x.EnergyNum
	}
	return 0
}

type GmCmdModifyEnergyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdModifyEnergyRsp) Reset() {
	*x = GmCmdModifyEnergyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdModifyEnergyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdModifyEnergyRsp) ProtoMessage() {}

func (x *GmCmdModifyEnergyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdModifyEnergyRsp.ProtoReflect.Descriptor instead.
func (*GmCmdModifyEnergyRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{31}
}

// ------------------------------------
//
//	hook
//
// ------------------------------------
// GmCmdOperateHookFishReq 玩家中鱼操作请求
type GmCmdOperateHookFishReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=PlayerId,proto3" json:"player_id,omitempty"` // 玩家id
	Operate  int32  `protobuf:"varint,2,opt,name=operate,json=Operate,proto3" json:"operate,omitempty"`      // 1:修改 2:删除 3:随机一条配置的鱼(不按概率计算) 4:查询
	FishId   int64  `protobuf:"varint,3,opt,name=fish_id,json=FishId,proto3" json:"fish_id,omitempty"`       // 鱼id
}

func (x *GmCmdOperateHookFishReq) Reset() {
	*x = GmCmdOperateHookFishReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdOperateHookFishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdOperateHookFishReq) ProtoMessage() {}

func (x *GmCmdOperateHookFishReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdOperateHookFishReq.ProtoReflect.Descriptor instead.
func (*GmCmdOperateHookFishReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{32}
}

func (x *GmCmdOperateHookFishReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GmCmdOperateHookFishReq) GetOperate() int32 {
	if x != nil {
		return x.Operate
	}
	return 0
}

func (x *GmCmdOperateHookFishReq) GetFishId() int64 {
	if x != nil {
		return x.FishId
	}
	return 0
}

// GmCmdOperateHookFishRsp 中鱼操作结果
type GmCmdOperateHookFishRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GmCmdOperateHookFishRsp) Reset() {
	*x = GmCmdOperateHookFishRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdOperateHookFishRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdOperateHookFishRsp) ProtoMessage() {}

func (x *GmCmdOperateHookFishRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdOperateHookFishRsp.ProtoReflect.Descriptor instead.
func (*GmCmdOperateHookFishRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{33}
}

// 强制刷新排期
type GmCmdFlushRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankId int64 `protobuf:"varint,1,opt,name=rank_id,json=rankId,proto3" json:"rank_id,omitempty"` // 排行id
}

func (x *GmCmdFlushRankReq) Reset() {
	*x = GmCmdFlushRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdFlushRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdFlushRankReq) ProtoMessage() {}

func (x *GmCmdFlushRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdFlushRankReq.ProtoReflect.Descriptor instead.
func (*GmCmdFlushRankReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{34}
}

func (x *GmCmdFlushRankReq) GetRankId() int64 {
	if x != nil {
		return x.RankId
	}
	return 0
}

type GmCmdFlushRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                      // 返回结果
	RankId int64          `protobuf:"varint,2,opt,name=rank_id,json=rankId,proto3" json:"rank_id,omitempty"` // 排行id
}

func (x *GmCmdFlushRankRsp) Reset() {
	*x = GmCmdFlushRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdFlushRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdFlushRankRsp) ProtoMessage() {}

func (x *GmCmdFlushRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdFlushRankRsp.ProtoReflect.Descriptor instead.
func (*GmCmdFlushRankRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{35}
}

func (x *GmCmdFlushRankRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GmCmdFlushRankRsp) GetRankId() int64 {
	if x != nil {
		return x.RankId
	}
	return 0
}

// 强制发奖
type GmCmdRewardRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankId int64 `protobuf:"varint,1,opt,name=rank_id,json=rankId,proto3" json:"rank_id,omitempty"` // 排行id
}

func (x *GmCmdRewardRankReq) Reset() {
	*x = GmCmdRewardRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdRewardRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdRewardRankReq) ProtoMessage() {}

func (x *GmCmdRewardRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdRewardRankReq.ProtoReflect.Descriptor instead.
func (*GmCmdRewardRankReq) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{36}
}

func (x *GmCmdRewardRankReq) GetRankId() int64 {
	if x != nil {
		return x.RankId
	}
	return 0
}

type GmCmdRewardRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                      // 返回结果
	RankId int64          `protobuf:"varint,2,opt,name=rank_id,json=rankId,proto3" json:"rank_id,omitempty"` // 排行id
}

func (x *GmCmdRewardRankRsp) Reset() {
	*x = GmCmdRewardRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gm_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GmCmdRewardRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmCmdRewardRankRsp) ProtoMessage() {}

func (x *GmCmdRewardRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_gm_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmCmdRewardRankRsp.ProtoReflect.Descriptor instead.
func (*GmCmdRewardRankRsp) Descriptor() ([]byte, []int) {
	return file_gm_proto_rawDescGZIP(), []int{37}
}

func (x *GmCmdRewardRankRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GmCmdRewardRankRsp) GetRankId() int64 {
	if x != nil {
		return x.RankId
	}
	return 0
}

var File_gm_proto protoreflect.FileDescriptor

var file_gm_proto_rawDesc = []byte{
	0x0a, 0x08, 0x67, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x6d, 0x50, 0x42,
	0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x01, 0x0a, 0x0c, 0x47, 0x6d, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x70, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x63, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x65, 0x72, 0x63, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x4d,
	0x5f, 0x43, 0x4d, 0x44, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x03, 0x63, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x03, 0x63, 0x69,
	0x64, 0x22, 0x56, 0x0a, 0x0c, 0x47, 0x6d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3a, 0x0a, 0x10, 0x47, 0x6d, 0x43,
	0x6d, 0x64, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x47, 0x61,
	0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x30, 0x0a, 0x14, 0x47, 0x6d, 0x43,
	0x6d, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x47,
	0x6d, 0x43, 0x6d, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x52, 0x73, 0x70, 0x22, 0xe5, 0x01, 0x0a, 0x13, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x52, 0x0d, 0x49, 0x74, 0x65, 0x6d, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x49, 0x73, 0x55, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x22, 0x46, 0x0a, 0x13, 0x47,
	0x6d, 0x43, 0x6d, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x73, 0x70, 0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x86, 0x01, 0x0a, 0x15, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x43, 0x6c, 0x65,
	0x61, 0x72, 0x43, 0x61, 0x74, 0x6f, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x52, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x17, 0x0a, 0x15,
	0x47, 0x6d, 0x43, 0x6d, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x43, 0x61, 0x74, 0x6f, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x73, 0x70, 0x22, 0x34, 0x0a, 0x13, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4c, 0x6f,
	0x61, 0x64, 0x52, 0x69, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x47,
	0x6d, 0x43, 0x6d, 0x64, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x69, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x72, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x69,
	0x6e, 0x75, 0x6f, 0x75, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x4c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x73, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x49,
	0x73, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x22, 0x3b, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x52, 0x65, 0x74, 0x22, 0xf6, 0x02, 0x0a, 0x11, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x44, 0x4b, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x11, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x44, 0x4b, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x52, 0x10, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x63, 0x64, 0x6b, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x64,
	0x6b, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x63, 0x64, 0x6b, 0x5f, 0x75, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x64, 0x6b, 0x55, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x35, 0x0a, 0x11,
	0x47, 0x6d, 0x43, 0x6d, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x44, 0x4b, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x44, 0x4b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x35, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x43, 0x44, 0x4b, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x85, 0x02, 0x0a, 0x0f, 0x43, 0x44, 0x4b,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x0a,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x64, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x64, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x44, 0x4b, 0x5f, 0x42, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0xb9, 0x01, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x44, 0x4b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x35, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x07, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x6d, 0x50, 0x42, 0x2e, 0x43, 0x44, 0x4b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x22, 0x65, 0x0a, 0x17,
	0x47, 0x6d, 0x43, 0x6d, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x44, 0x4b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x43, 0x44, 0x4b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x64, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x63, 0x64, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x75, 0x73, 0x65,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x43, 0x44, 0x4b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x35, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2d, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x67, 0x6d, 0x50, 0x42, 0x2e, 0x43, 0x44, 0x4b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x55,
	0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x44,
	0x4b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x44, 0x4b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x22, 0x7c, 0x0a, 0x13, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x67, 0x6d, 0x50, 0x42, 0x2e, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x52, 0x07, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x22, 0x15, 0x0a, 0x13, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x22, 0x7f, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x73,
	0x75, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x53, 0x75, 0x62,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x3b, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d,
	0x64, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x52, 0x65, 0x74, 0x22, 0x89, 0x01, 0x0a, 0x15, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x75,
	0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x6d,
	0x43, 0x6d, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x4e, 0x75, 0x6d, 0x22, 0x16,
	0x0a, 0x14, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x45, 0x6e, 0x65,
	0x72, 0x67, 0x79, 0x52, 0x73, 0x70, 0x22, 0x69, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x69, 0x73, 0x68, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x73, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x46, 0x69, 0x73, 0x68, 0x49,
	0x64, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x6d, 0x43, 0x6d, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x69, 0x73, 0x68, 0x52, 0x73, 0x70, 0x22, 0x2c, 0x0a, 0x11,
	0x47, 0x6d, 0x43, 0x6d, 0x64, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x11, 0x47, 0x6d,
	0x43, 0x6d, 0x64, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x6d,
	0x43, 0x6d, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x12, 0x17, 0x0a, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x12, 0x47, 0x6d, 0x43,
	0x6d, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x2a, 0x76, 0x0a, 0x0f, 0x47, 0x4d,
	0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x12, 0x12, 0x0a,
	0x0e, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10,
	0x00, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x4d,
	0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49,
	0x4e, 0x49, 0x53, 0x48, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53,
	0x4b, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x10, 0x03, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61,
	0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64,
	0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x67, 0x6d, 0x3b, 0x67, 0x6d, 0x50, 0x42, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gm_proto_rawDescOnce sync.Once
	file_gm_proto_rawDescData = file_gm_proto_rawDesc
)

func file_gm_proto_rawDescGZIP() []byte {
	file_gm_proto_rawDescOnce.Do(func() {
		file_gm_proto_rawDescData = protoimpl.X.CompressGZIP(file_gm_proto_rawDescData)
	})
	return file_gm_proto_rawDescData
}

var file_gm_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_gm_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_gm_proto_goTypes = []interface{}{
	(GM_TASK_OPERATE)(0),              // 0: gmPB.GM_TASK_OPERATE
	(*GmOperateReq)(nil),              // 1: gmPB.GmOperateReq
	(*GmOperateRsp)(nil),              // 2: gmPB.GmOperateRsp
	(*GmCmdGameTimeReq)(nil),          // 3: gmPB.GmCmdGameTimeReq
	(*GmCmdGameTimeRsp)(nil),          // 4: gmPB.GmCmdGameTimeRsp
	(*GmCmdClearWeatherReq)(nil),      // 5: gmPB.GmCmdClearWeatherReq
	(*GmCmdClearWeatherRsp)(nil),      // 6: gmPB.GmCmdClearWeatherRsp
	(*GmCmdOperateItemReq)(nil),       // 7: gmPB.GmCmdOperateItemReq
	(*GmCmdOperateItemRsp)(nil),       // 8: gmPB.GmCmdOperateItemRsp
	(*GmCmdClearCatogoryReq)(nil),     // 9: gmPB.GmCmdClearCatogoryReq
	(*GmCmdClearCatogoryRsp)(nil),     // 10: gmPB.GmCmdClearCatogoryRsp
	(*GmCmdLoadRigRuleReq)(nil),       // 11: gmPB.GmCmdLoadRigRuleReq
	(*GmCmdLoadRigRuleRsp)(nil),       // 12: gmPB.GmCmdLoadRigRuleRsp
	(*GmCmdContinuousLoginReq)(nil),   // 13: gmPB.GmCmdContinuousLoginReq
	(*GmCmdContinuousLoginRsp)(nil),   // 14: gmPB.GmCmdContinuousLoginRsp
	(*GmCmdCreateCDKReq)(nil),         // 15: gmPB.GmCmdCreateCDKReq
	(*GmCmdCreateCDKRsp)(nil),         // 16: gmPB.GmCmdCreateCDKRsp
	(*GmCmdQueryCDKBatchesReq)(nil),   // 17: gmPB.GmCmdQueryCDKBatchesReq
	(*CDKBatchSummary)(nil),           // 18: gmPB.CDKBatchSummary
	(*GmCmdQueryCDKBatchesRsp)(nil),   // 19: gmPB.GmCmdQueryCDKBatchesRsp
	(*GmCmdQueryCDKRecordsReq)(nil),   // 20: gmPB.GmCmdQueryCDKRecordsReq
	(*CDKRecordInfo)(nil),             // 21: gmPB.CDKRecordInfo
	(*GmCmdQueryCDKRecordsRsp)(nil),   // 22: gmPB.GmCmdQueryCDKRecordsRsp
	(*GmCmdDisableCDKBatchReq)(nil),   // 23: gmPB.GmCmdDisableCDKBatchReq
	(*GmCmdDisableCDKBatchRsp)(nil),   // 24: gmPB.GmCmdDisableCDKBatchRsp
	(*GmCmdOperateTaskReq)(nil),       // 25: gmPB.GmCmdOperateTaskReq
	(*GmCmdOperateTaskRsp)(nil),       // 26: gmPB.GmCmdOperateTaskRsp
	(*GmCmdTaskProgressSetReq)(nil),   // 27: gmPB.GmCmdTaskProgressSetReq
	(*GmCmdTaskProgressSetRsp)(nil),   // 28: gmPB.GmCmdTaskProgressSetRsp
	(*GmCmdTaskSubUpdateReq)(nil),     // 29: gmPB.GmCmdTaskSubUpdateReq
	(*GmCmdTaskSubUpdateRsp)(nil),     // 30: gmPB.GmCmdTaskSubUpdateRsp
	(*GmCmdModifyEnergyReq)(nil),      // 31: gmPB.GmCmdModifyEnergyReq
	(*GmCmdModifyEnergyRsp)(nil),      // 32: gmPB.GmCmdModifyEnergyRsp
	(*GmCmdOperateHookFishReq)(nil),   // 33: gmPB.GmCmdOperateHookFishReq
	(*GmCmdOperateHookFishRsp)(nil),   // 34: gmPB.GmCmdOperateHookFishRsp
	(*GmCmdFlushRankReq)(nil),         // 35: gmPB.GmCmdFlushRankReq
	(*GmCmdFlushRankRsp)(nil),         // 36: gmPB.GmCmdFlushRankRsp
	(*GmCmdRewardRankReq)(nil),        // 37: gmPB.GmCmdRewardRankReq
	(*GmCmdRewardRankRsp)(nil),        // 38: gmPB.GmCmdRewardRankRsp
	(common.PRODUCT_ID)(0),            // 39: common.PRODUCT_ID
	(common.GM_CMD)(0),                // 40: common.GM_CMD
	(common.CHANNEL_TYPE)(0),          // 41: common.CHANNEL_TYPE
	(*common.Result)(nil),             // 42: common.Result
	(common.ITEM_OPERATION)(0),        // 43: common.ITEM_OPERATION
	(*common.Reward)(nil),             // 44: common.Reward
	(common.ITEM_CATEGORY)(0),         // 45: common.ITEM_CATEGORY
	(common.CDK_GENERATION_OPTION)(0), // 46: common.CDK_GENERATION_OPTION
	(*common.ItemBase)(nil),           // 47: common.ItemBase
	(*common.PaginationReq)(nil),      // 48: common.PaginationReq
	(common.CDK_BATCH_STATUS)(0),      // 49: common.CDK_BATCH_STATUS
}
var file_gm_proto_depIdxs = []int32{
	39, // 0: gmPB.GmOperateReq.pid:type_name -> common.PRODUCT_ID
	40, // 1: gmPB.GmOperateReq.cmd:type_name -> common.GM_CMD
	41, // 2: gmPB.GmOperateReq.cid:type_name -> common.CHANNEL_TYPE
	42, // 3: gmPB.GmOperateRsp.ret:type_name -> common.Result
	43, // 4: gmPB.GmCmdOperateItemReq.item_operation:type_name -> common.ITEM_OPERATION
	44, // 5: gmPB.GmCmdOperateItemRsp.reward_info:type_name -> common.Reward
	45, // 6: gmPB.GmCmdClearCatogoryReq.category:type_name -> common.ITEM_CATEGORY
	42, // 7: gmPB.GmCmdContinuousLoginRsp.ret:type_name -> common.Result
	46, // 8: gmPB.GmCmdCreateCDKReq.generation_option:type_name -> common.CDK_GENERATION_OPTION
	47, // 9: gmPB.GmCmdCreateCDKReq.rewards:type_name -> common.ItemBase
	42, // 10: gmPB.GmCmdCreateCDKRsp.ret:type_name -> common.Result
	48, // 11: gmPB.GmCmdQueryCDKBatchesReq.pagination:type_name -> common.PaginationReq
	49, // 12: gmPB.GmCmdQueryCDKBatchesReq.status:type_name -> common.CDK_BATCH_STATUS
	41, // 13: gmPB.CDKBatchSummary.channel_id:type_name -> common.CHANNEL_TYPE
	49, // 14: gmPB.CDKBatchSummary.status:type_name -> common.CDK_BATCH_STATUS
	42, // 15: gmPB.GmCmdQueryCDKBatchesRsp.ret:type_name -> common.Result
	48, // 16: gmPB.GmCmdQueryCDKBatchesRsp.pagination:type_name -> common.PaginationReq
	18, // 17: gmPB.GmCmdQueryCDKBatchesRsp.batches:type_name -> gmPB.CDKBatchSummary
	42, // 18: gmPB.GmCmdQueryCDKRecordsRsp.ret:type_name -> common.Result
	48, // 19: gmPB.GmCmdQueryCDKRecordsRsp.pagination:type_name -> common.PaginationReq
	21, // 20: gmPB.GmCmdQueryCDKRecordsRsp.records:type_name -> gmPB.CDKRecordInfo
	42, // 21: gmPB.GmCmdDisableCDKBatchRsp.ret:type_name -> common.Result
	0,  // 22: gmPB.GmCmdOperateTaskReq.operate:type_name -> gmPB.GM_TASK_OPERATE
	42, // 23: gmPB.GmCmdTaskProgressSetRsp.ret:type_name -> common.Result
	42, // 24: gmPB.GmCmdFlushRankRsp.ret:type_name -> common.Result
	42, // 25: gmPB.GmCmdRewardRankRsp.ret:type_name -> common.Result
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_gm_proto_init() }
func file_gm_proto_init() {
	if File_gm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmOperateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmOperateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdGameTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdGameTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdClearWeatherReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdClearWeatherRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdOperateItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdOperateItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdClearCatogoryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdClearCatogoryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdLoadRigRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdLoadRigRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdContinuousLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdContinuousLoginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdCreateCDKReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdCreateCDKRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdQueryCDKBatchesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDKBatchSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdQueryCDKBatchesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdQueryCDKRecordsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDKRecordInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdQueryCDKRecordsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdDisableCDKBatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdDisableCDKBatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdOperateTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdOperateTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdTaskProgressSetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdTaskProgressSetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdTaskSubUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdTaskSubUpdateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdModifyEnergyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdModifyEnergyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdOperateHookFishReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdOperateHookFishRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdFlushRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdFlushRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdRewardRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gm_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GmCmdRewardRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gm_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gm_proto_goTypes,
		DependencyIndexes: file_gm_proto_depIdxs,
		EnumInfos:         file_gm_proto_enumTypes,
		MessageInfos:      file_gm_proto_msgTypes,
	}.Build()
	File_gm_proto = out.File
	file_gm_proto_rawDesc = nil
	file_gm_proto_goTypes = nil
	file_gm_proto_depIdxs = nil
}

# Grafana 接入 ClickHouse 详细指南

## 目录
1. [概述](#概述)
2. [环境准备](#环境准备)
3. [ClickHouse 配置](#clickhouse-配置)
4. [Grafana 安装与配置](#grafana-安装与配置)
5. [数据源配置](#数据源配置)
6. [仪表板创建](#仪表板创建)
7. [查询优化](#查询优化)
8. [监控与告警](#监控与告警)
9. [最佳实践](#最佳实践)
10. [故障排除](#故障排除)

## 概述

### 什么是 ClickHouse
ClickHouse 是一个用于在线分析处理 (OLAP) 的列式数据库管理系统，专为实时分析查询而设计。它具有以下特点：
- 极高的查询性能
- 列式存储
- 数据压缩
- 分布式架构支持
- SQL 兼容性

### 什么是 Grafana
Grafana 是一个开源的监控和可观测性平台，用于：
- 数据可视化
- 仪表板创建
- 告警管理
- 多数据源支持

### 集成优势
- **高性能分析**: ClickHouse 的列式存储和查询优化与 Grafana 的可视化能力完美结合
- **实时监控**: 支持大规模数据的实时查询和展示
- **灵活的可视化**: 丰富的图表类型和自定义选项
- **成本效益**: 开源解决方案，降低企业成本

## 环境准备

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+, CentOS 7+)
- **内存**: 最少 4GB，推荐 8GB+
- **存储**: SSD 推荐，至少 50GB 可用空间
- **网络**: 稳定的网络连接

### 依赖软件
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget gnupg2 software-properties-common
```

## ClickHouse 配置

### 安装 ClickHouse

#### 方法一：使用官方仓库
```bash
# 添加 ClickHouse 官方仓库
curl -fsSL 'https://packages.clickhouse.com/rpm/lts/repodata/repomd.xml.key' | sudo gpg --dearmor -o /usr/share/keyrings/clickhouse-keyring.gpg

echo "deb [signed-by=/usr/share/keyrings/clickhouse-keyring.gpg] https://packages.clickhouse.com/deb stable main" | sudo tee /etc/apt/sources.list.d/clickhouse.list

# 安装 ClickHouse
sudo apt update
sudo apt install -y clickhouse-server clickhouse-client
```

#### 方法二：使用 Docker
```bash
# 创建数据目录
mkdir -p /opt/clickhouse/data /opt/clickhouse/logs /opt/clickhouse/config

# 运行 ClickHouse 容器
docker run -d \
  --name clickhouse-server \
  --ulimit nofile=262144:262144 \
  -p 8123:8123 \
  -p 9000:9000 \
  -v /opt/clickhouse/data:/var/lib/clickhouse \
  -v /opt/clickhouse/logs:/var/log/clickhouse-server \
  clickhouse/clickhouse-server:latest
```

### ClickHouse 基础配置

#### 配置文件位置
- 主配置文件: `/etc/clickhouse-server/config.xml`
- 用户配置文件: `/etc/clickhouse-server/users.xml`

#### 网络配置
```xml
<!-- /etc/clickhouse-server/config.xml -->
<clickhouse>
    <!-- 监听所有接口 -->
    <listen_host>0.0.0.0</listen_host>
    
    <!-- HTTP 端口 -->
    <http_port>8123</http_port>
    
    <!-- TCP 端口 -->
    <tcp_port>9000</tcp_port>
    
    <!-- 数据目录 -->
    <path>/var/lib/clickhouse/</path>
    
    <!-- 临时数据目录 -->
    <tmp_path>/var/lib/clickhouse/tmp/</tmp_path>
    
    <!-- 用户文件目录 -->
    <user_files_path>/var/lib/clickhouse/user_files/</user_files_path>
</clickhouse>
```

#### 用户权限配置
```xml
<!-- /etc/clickhouse-server/users.xml -->
<clickhouse>
    <users>
        <!-- 默认用户 -->
        <default>
            <password></password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>default</profile>
            <quota>default</quota>
        </default>
        
        <!-- Grafana 专用用户 -->
        <grafana>
            <password>your_secure_password</password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>readonly</profile>
            <quota>default</quota>
        </grafana>
    </users>
    
    <profiles>
        <default>
            <max_memory_usage>10000000000</max_memory_usage>
            <use_uncompressed_cache>0</use_uncompressed_cache>
            <load_balancing>random</load_balancing>
        </default>
        
        <readonly>
            <readonly>1</readonly>
            <max_memory_usage>**********</max_memory_usage>
            <use_uncompressed_cache>0</use_uncompressed_cache>
        </readonly>
    </profiles>
</clickhouse>
```

### 启动 ClickHouse 服务
```bash
# 启动服务
sudo systemctl start clickhouse-server

# 设置开机自启
sudo systemctl enable clickhouse-server

# 检查服务状态
sudo systemctl status clickhouse-server

# 验证连接
clickhouse-client --query "SELECT version()"
```

### 创建示例数据库和表
```sql
-- 连接到 ClickHouse
clickhouse-client

-- 创建数据库
CREATE DATABASE monitoring;

-- 使用数据库
USE monitoring;

-- 创建系统指标表
CREATE TABLE system_metrics (
    timestamp DateTime,
    hostname String,
    cpu_usage Float64,
    memory_usage Float64,
    disk_usage Float64,
    network_in Float64,
    network_out Float64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (hostname, timestamp);

-- 插入示例数据
INSERT INTO system_metrics VALUES
    (now() - 3600, 'server1', 45.2, 67.8, 23.1, 1024.5, 2048.3),
    (now() - 3000, 'server1', 52.1, 71.2, 23.5, 1156.7, 2234.1),
    (now() - 2400, 'server1', 38.9, 65.4, 24.0, 987.3, 1876.9),
    (now() - 1800, 'server2', 41.7, 58.3, 45.2, 2048.1, 3072.5),
    (now() - 1200, 'server2', 47.3, 62.1, 46.1, 2234.7, 3456.2),
    (now() - 600, 'server2', 39.8, 55.9, 47.3, 1987.4, 2987.8);
```

## Grafana 安装与配置

### 安装 Grafana

#### 方法一：使用官方仓库
```bash
# 添加 Grafana 官方仓库
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
echo "deb https://packages.grafana.com/oss/deb stable main" | sudo tee -a /etc/apt/sources.list.d/grafana.list

# 安装 Grafana
sudo apt update
sudo apt install grafana
```

#### 方法二：使用 Docker
```bash
# 创建配置目录
mkdir -p /opt/grafana/data /opt/grafana/logs /opt/grafana/plugins

# 运行 Grafana 容器
docker run -d \
  --name grafana \
  -p 3000:3000 \
  -v /opt/grafana/data:/var/lib/grafana \
  -v /opt/grafana/logs:/var/log/grafana \
  -v /opt/grafana/plugins:/var/lib/grafana/plugins \
  -e "GF_SECURITY_ADMIN_PASSWORD=admin123" \
  grafana/grafana:latest
```

### Grafana 基础配置

#### 配置文件
```ini
# /etc/grafana/grafana.ini

[server]
# HTTP 端口
http_port = 3000

# 域名
domain = localhost

# 根 URL
root_url = http://localhost:3000/

[database]
# 数据库类型
type = sqlite3

# 数据库路径
path = grafana.db

[security]
# 管理员用户
admin_user = admin

# 管理员密码
admin_password = admin123

# 密钥
secret_key = SW2YcwTIb9zpOOhoPsMm

[users]
# 允许注册
allow_sign_up = false

# 允许组织创建
allow_org_create = false

[auth.anonymous]
# 启用匿名访问
enabled = false

[logging]
# 日志级别
level = info

# 日志模式
mode = console file

[log.console]
level = info

[log.file]
level = info
log_rotate = true
max_lines = 1000000
max_size_shift = 28
daily_rotate = true
max_days = 7
```

### 启动 Grafana 服务
```bash
# 启动服务
sudo systemctl start grafana-server

# 设置开机自启
sudo systemctl enable grafana-server

# 检查服务状态
sudo systemctl status grafana-server
```

### 安装 ClickHouse 插件

#### 方法一：通过 Grafana CLI
```bash
# 安装 ClickHouse 数据源插件
sudo grafana-cli plugins install grafana-clickhouse-datasource

# 重启 Grafana
sudo systemctl restart grafana-server
```

#### 方法二：手动安装
```bash
# 下载插件
cd /var/lib/grafana/plugins
sudo wget https://github.com/grafana/clickhouse-datasource/releases/latest/download/grafana-clickhouse-datasource.zip

# 解压插件
sudo unzip grafana-clickhouse-datasource.zip

# 设置权限
sudo chown -R grafana:grafana /var/lib/grafana/plugins/

# 重启 Grafana
sudo systemctl restart grafana-server
```

## 数据源配置

### 登录 Grafana
1. 打开浏览器访问 `http://localhost:3000`
2. 使用默认凭据登录：
   - 用户名: `admin`
   - 密码: `admin123`

### 添加 ClickHouse 数据源

#### 步骤一：进入数据源配置
1. 点击左侧菜单的齿轮图标 (Configuration)
2. 选择 "Data Sources"
3. 点击 "Add data source"
4. 搜索并选择 "ClickHouse"

#### 步骤二：配置连接参数
```yaml
# 基本配置
Name: ClickHouse-Production
Default: true

# HTTP 配置
URL: http://localhost:8123
Access: Server (default)

# 认证配置
Auth:
  Basic auth: false
  With Credentials: false

# ClickHouse 详细配置
Server Address: localhost
Server Port: 8123
Protocol: http
Username: grafana
Password: your_secure_password
Default Database: monitoring

# 高级配置
Additional Settings:
  Timeout: 30s
  Max Open Connections: 10
  Max Idle Connections: 5
  Connection Max Lifetime: 300s
```

#### 步骤三：测试连接
1. 点击 "Save & Test" 按钮
2. 确认显示 "Data source is working" 消息

### 高级连接配置

#### HTTPS 连接
```yaml
URL: https://your-clickhouse-server:8443
TLS/SSL Settings:
  Skip TLS Verify: false
  TLS Client Auth: false
  Server Name: your-clickhouse-server
```

#### 集群配置
```yaml
# 负载均衡配置
Server Address: clickhouse-cluster.example.com
Additional Settings:
  Load Balancing: round_robin
  Failover: true
  Health Check Interval: 30s
```

## 仪表板创建

### 创建第一个仪表板

#### 步骤一：新建仪表板
1. 点击左侧菜单的 "+" 图标
2. 选择 "Dashboard"
3. 点击 "Add new panel"

#### 步骤二：配置查询

##### 基础查询示例
```sql
-- CPU 使用率时间序列
SELECT 
    timestamp,
    hostname,
    cpu_usage
FROM monitoring.system_metrics
WHERE timestamp >= now() - INTERVAL 1 HOUR
ORDER BY timestamp
```

##### 聚合查询示例
```sql
-- 平均 CPU 使用率（按主机分组）
SELECT 
    toStartOfMinute(timestamp) as time,
    hostname,
    avg(cpu_usage) as avg_cpu
FROM monitoring.system_metrics
WHERE timestamp >= $__timeFrom AND timestamp <= $__timeTo
GROUP BY time, hostname
ORDER BY time
```

##### 复杂分析查询
```sql
-- 系统资源使用率趋势
SELECT 
    toStartOfHour(timestamp) as time,
    avg(cpu_usage) as avg_cpu,
    avg(memory_usage) as avg_memory,
    avg(disk_usage) as avg_disk,
    max(cpu_usage) as max_cpu,
    max(memory_usage) as max_memory
FROM monitoring.system_metrics
WHERE timestamp >= $__timeFrom AND timestamp <= $__timeTo
GROUP BY time
ORDER BY time
```

### 可视化类型配置

#### 时间序列图表
```yaml
Panel Type: Time series
Query:
  Format: Time series
  
Visualization Options:
  Draw style: Line
  Line interpolation: Linear
  Line width: 1
  Fill opacity: 10
  Gradient mode: None
  
Axes:
  Left Y-axis:
    Unit: percent (0-100)
    Min: 0
    Max: 100
  
Legend:
  Display mode: Table
  Placement: Bottom
  Values: Last, Max, Min
```

#### 统计面板
```yaml
Panel Type: Stat
Query:
  Format: Table
  
Visualization Options:
  Value: Last
  Unit: percent
  Decimal: 1
  
Thresholds:
  - Value: 80, Color: Yellow
  - Value: 90, Color: Red
  
Color mode: Value
Graph mode: Area
```

#### 表格面板
```yaml
Panel Type: Table
Query:
  Format: Table
  
Column Options:
  - Field: hostname, Width: 150
  - Field: cpu_usage, Width: 100, Unit: percent
  - Field: memory_usage, Width: 100, Unit: percent
  
Table Options:
  Show header: true
  Sort by: cpu_usage (desc)
```

### 变量配置

#### 创建主机名变量
```yaml
Variable Name: hostname
Type: Query
Data source: ClickHouse-Production
Query: SELECT DISTINCT hostname FROM monitoring.system_metrics
Refresh: On Dashboard Load
Multi-value: true
Include All option: true
```

#### 创建时间范围变量
```yaml
Variable Name: time_range
Type: Interval
Values: 1m,5m,15m,30m,1h,6h,12h,1d
Auto Option: true
```

#### 在查询中使用变量
```sql
SELECT 
    toStartOfInterval(timestamp, INTERVAL $time_range) as time,
    hostname,
    avg(cpu_usage) as avg_cpu
FROM monitoring.system_metrics
WHERE hostname IN ($hostname)
  AND timestamp >= $__timeFrom 
  AND timestamp <= $__timeTo
GROUP BY time, hostname
ORDER BY time
```

## 查询优化

### ClickHouse 查询优化技巧

#### 1. 使用适当的索引
```sql
-- 创建带有合适排序键的表
CREATE TABLE optimized_metrics (
    timestamp DateTime,
    hostname LowCardinality(String),
    metric_name LowCardinality(String),
    value Float64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (hostname, metric_name, timestamp)
SETTINGS index_granularity = 8192;
```

#### 2. 分区策略
```sql
-- 按月分区
PARTITION BY toYYYYMM(timestamp)

-- 按日分区（高频数据）
PARTITION BY toYYYYMMDD(timestamp)

-- 按主机和月份分区
PARTITION BY (hostname, toYYYYMM(timestamp))
```

#### 3. 数据类型优化
```sql
-- 使用 LowCardinality 优化字符串
hostname LowCardinality(String),
status LowCardinality(String),

-- 使用合适的数值类型
cpu_usage Float32,  -- 而不是 Float64
memory_bytes UInt64,
small_counter UInt32  -- 而不是 UInt64
```

#### 4. 查询优化技巧
```sql
-- 使用 PREWHERE 进行早期过滤
SELECT timestamp, hostname, cpu_usage
FROM system_metrics
PREWHERE hostname = 'server1'
WHERE timestamp >= now() - INTERVAL 1 HOUR;

-- 使用采样减少数据量
SELECT 
    toStartOfMinute(timestamp) as time,
    avg(cpu_usage) as avg_cpu
FROM system_metrics SAMPLE 0.1  -- 10% 采样
WHERE timestamp >= now() - INTERVAL 1 DAY
GROUP BY time
ORDER BY time;

-- 使用物化视图预聚合
CREATE MATERIALIZED VIEW hourly_metrics_mv
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(hour)
ORDER BY (hostname, hour)
AS SELECT
    toStartOfHour(timestamp) as hour,
    hostname,
    avg(cpu_usage) as avg_cpu,
    avg(memory_usage) as avg_memory,
    count() as samples
FROM system_metrics
GROUP BY hour, hostname;
```

### Grafana 查询优化

#### 1. 使用时间宏
```sql
-- 使用 Grafana 时间宏
WHERE timestamp >= $__timeFrom AND timestamp <= $__timeTo

-- 使用时间过滤器宏
WHERE $__timeFilter(timestamp)

-- 使用时间分组宏
SELECT 
    $__timeGroup(timestamp, $__interval) as time,
    avg(cpu_usage) as value
FROM system_metrics
WHERE $__timeFilter(timestamp)
GROUP BY time
ORDER BY time
```

#### 2. 限制返回数据量
```sql
-- 使用 LIMIT 限制结果
SELECT * FROM system_metrics
WHERE $__timeFilter(timestamp)
ORDER BY timestamp DESC
LIMIT 10000;

-- 使用聚合减少数据点
SELECT 
    toStartOfInterval(timestamp, INTERVAL $__interval) as time,
    avg(cpu_usage) as avg_cpu
FROM system_metrics
WHERE $__timeFilter(timestamp)
GROUP BY time
ORDER BY time;
```

#### 3. 缓存策略
```yaml
# 在数据源配置中设置缓存
Cache Settings:
  Query Cache TTL: 300s
  Query Cache Max Size: 100MB
```

## 监控与告警

### 配置告警规则

#### 创建告警规则
1. 在面板中点击 "Alert" 标签
2. 点击 "Create Alert"
3. 配置告警条件

#### CPU 使用率告警示例
```yaml
Alert Rule:
  Name: High CPU Usage
  
Query:
  A: SELECT avg(cpu_usage) FROM system_metrics 
     WHERE hostname = '$hostname' 
     AND timestamp >= now() - INTERVAL 5 MINUTE

Conditions:
  - Query: A
  - Reducer: avg
  - Evaluator: is above 80

Execution:
  Evaluate every: 1m
  For: 5m

Notifications:
  Send to: team-alerts
  Message: "CPU usage is above 80% on {{$labels.hostname}}"
```

#### 内存使用率告警
```yaml
Alert Rule:
  Name: High Memory Usage
  
Query:
  A: SELECT avg(memory_usage) FROM system_metrics 
     WHERE timestamp >= now() - INTERVAL 5 MINUTE
     GROUP BY hostname

Conditions:
  - Query: A
  - Reducer: avg
  - Evaluator: is above 90

Execution:
  Evaluate every: 30s
  For: 2m
```

### 通知渠道配置

#### 邮件通知
```yaml
Type: Email
Name: team-email
Settings:
  Addresses: <EMAIL>, <EMAIL>
  Subject: "[ALERT] {{range .Alerts}}{{.AlertName}}{{end}}"
  Body: |
    {{ range .Alerts }}
    Alert: {{ .AlertName }}
    Status: {{ .Status }}
    Host: {{ .Labels.hostname }}
    Value: {{ .Value }}
    {{ end }}
```

#### Slack 通知
```yaml
Type: Slack
Name: team-slack
Settings:
  URL: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
  Channel: #alerts
  Username: Grafana
  Title: "{{range .Alerts}}{{.AlertName}}{{end}}"
  Text: |
    {{ range .Alerts }}
    *Alert:* {{ .AlertName }}
    *Status:* {{ .Status }}
    *Host:* {{ .Labels.hostname }}
    *Value:* {{ .Value }}
    {{ end }}
```

#### Webhook 通知
```yaml
Type: Webhook
Name: custom-webhook
Settings:
  URL: https://your-api.com/alerts
  HTTP Method: POST
  Headers:
    Content-Type: application/json
    Authorization: Bearer YOUR_TOKEN
```

### 告警管理

#### 告警状态管理
```bash
# 查看活跃告警
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:3000/api/alerts

# 暂停告警
curl -X POST \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"paused": true}' \
     http://localhost:3000/api/alerts/1/pause
```

#### 告警历史查询
```sql
-- 查询告警历史（如果存储在 ClickHouse 中）
SELECT 
    timestamp,
    alert_name,
    hostname,
    status,
    value
FROM monitoring.alert_history
WHERE timestamp >= now() - INTERVAL 1 DAY
ORDER BY timestamp DESC;
```

## 最佳实践

### 数据建模最佳实践

#### 1. 表结构设计
```sql
-- 推荐的指标表结构
CREATE TABLE metrics (
    timestamp DateTime64(3),
    metric_name LowCardinality(String),
    tags Map(String, String),
    value Float64,
    hostname LowCardinality(String),
    service LowCardinality(String)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (metric_name, hostname, service, timestamp)
SETTINGS index_granularity = 8192;

-- 事件表结构
CREATE TABLE events (
    timestamp DateTime64(3),
    event_type LowCardinality(String),
    source LowCardinality(String),
    message String,
    tags Map(String, String),
    severity LowCardinality(String)
) ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (event_type, source, timestamp);
```

#### 2. 数据保留策略
```sql
-- 设置 TTL 自动删除旧数据
ALTER TABLE metrics 
MODIFY TTL timestamp + INTERVAL 90 DAY;

-- 分层存储策略
ALTER TABLE metrics 
MODIFY TTL 
    timestamp + INTERVAL 7 DAY TO DISK 'ssd',
    timestamp + INTERVAL 30 DAY TO DISK 'hdd',
    timestamp + INTERVAL 90 DAY DELETE;
```

### 性能优化最佳实践

#### 1. 查询模式优化
```sql
-- 使用预聚合视图
CREATE MATERIALIZED VIEW metrics_hourly
ENGINE = AggregatingMergeTree()
PARTITION BY toYYYYMM(hour)
ORDER BY (metric_name, hostname, hour)
AS SELECT
    toStartOfHour(timestamp) as hour,
    metric_name,
    hostname,
    avgState(value) as avg_value,
    maxState(value) as max_value,
    minState(value) as min_value,
    countState() as count_value
FROM metrics
GROUP BY hour, metric_name, hostname;

-- 查询预聚合数据
SELECT 
    hour,
    metric_name,
    hostname,
    avgMerge(avg_value) as avg_val,
    maxMerge(max_value) as max_val,
    minMerge(min_value) as min_val
FROM metrics_hourly
WHERE hour >= $__timeFrom AND hour <= $__timeTo
GROUP BY hour, metric_name, hostname
ORDER BY hour;
```

#### 2. 索引优化
```sql
-- 创建跳数索引
ALTER TABLE metrics 
ADD INDEX idx_value value TYPE minmax GRANULARITY 4;

-- 创建布隆过滤器索引
ALTER TABLE events 
ADD INDEX idx_message message TYPE bloom_filter GRANULARITY 1;
```

### 监控最佳实践

#### 1. 分层监控策略
```yaml
# 基础设施层
Infrastructure Metrics:
  - CPU, Memory, Disk, Network
  - System Load, Process Count
  - Hardware Health

# 应用层
Application Metrics:
  - Response Time, Throughput
  - Error Rate, Success Rate
  - Business Metrics

# 用户体验层
User Experience:
  - Page Load Time
  - User Journey Metrics
  - Conversion Rates
```

#### 2. 仪表板组织
```yaml
Dashboard Structure:
  - Overview Dashboard: 高级别指标概览
  - Infrastructure Dashboard: 基础设施详细监控
  - Application Dashboard: 应用性能监控
  - Business Dashboard: 业务指标监控
  - Troubleshooting Dashboard: 故障排查专用
```

#### 3. 告警策略
```yaml
Alert Levels:
  Critical: 
    - Service Down
    - Data Loss Risk
    - Security Breach
  
  Warning:
    - High Resource Usage
    - Performance Degradation
    - Capacity Planning
  
  Info:
    - Deployment Events
    - Configuration Changes
    - Maintenance Windows
```

### 安全最佳实践

#### 1. 访问控制
```sql
-- 创建只读用户
CREATE USER grafana_readonly 
IDENTIFIED BY 'secure_password'
SETTINGS readonly = 1;

-- 授予特定数据库权限
GRANT SELECT ON monitoring.* TO grafana_readonly;

-- 限制查询复杂度
CREATE SETTINGS PROFILE grafana_profile 
SETTINGS 
    max_memory_usage = 1000000000,
    max_execution_time = 60,
    max_rows_to_read = 1000000;

ALTER USER grafana_readonly 
SETTINGS PROFILE 'grafana_profile';
```

#### 2. 网络安全
```yaml
# ClickHouse 配置
Security Settings:
  - Enable HTTPS
  - Configure Firewall Rules
  - Use VPN/Private Networks
  - Enable Query Logging

# Grafana 配置
Security Settings:
  - Enable HTTPS
  - Configure OAuth/LDAP
  - Set Session Timeout
  - Enable Audit Logging
```

## 故障排除

### 常见问题及解决方案

#### 1. 连接问题

##### 问题：无法连接到 ClickHouse
```bash
# 检查 ClickHouse 服务状态
sudo systemctl status clickhouse-server

# 检查端口监听
netstat -tlnp | grep 8123

# 检查防火墙
sudo ufw status
sudo iptables -L

# 测试连接
curl http://localhost:8123/ping
```

##### 解决方案：
```bash
# 重启 ClickHouse 服务
sudo systemctl restart clickhouse-server

# 检查配置文件
sudo clickhouse-server --config-file=/etc/clickhouse-server/config.xml --check-config

# 查看日志
sudo tail -f /var/log/clickhouse-server/clickhouse-server.log
```

#### 2. 查询性能问题

##### 问题：查询响应缓慢
```sql
-- 检查查询执行计划
EXPLAIN SELECT * FROM system_metrics 
WHERE timestamp >= now() - INTERVAL 1 HOUR;

-- 检查系统查询日志
SELECT 
    query,
    query_duration_ms,
    read_rows,
    read_bytes,
    memory_usage
FROM system.query_log
WHERE event_time >= now() - INTERVAL 1 HOUR
  AND query_duration_ms > 1000
ORDER BY query_duration_ms DESC;
```

##### 解决方案：
```sql
-- 优化查询
-- 1. 添加适当的 WHERE 条件
-- 2. 使用 PREWHERE 进行早期过滤
-- 3. 添加 LIMIT 限制结果集
-- 4. 使用预聚合表

-- 检查表统计信息
SELECT 
    table,
    partition,
    rows,
    bytes_on_disk,
    data_compressed_bytes,
    data_uncompressed_bytes
FROM system.parts
WHERE table = 'system_metrics';
```

#### 3. 内存问题

##### 问题：内存使用过高
```sql
-- 检查内存使用情况
SELECT 
    query,
    memory_usage,
    peak_memory_usage,
    user
FROM system.processes
WHERE memory_usage > 1000000000;  -- 1GB
```

##### 解决方案：
```xml
<!-- 调整内存限制 -->
<profiles>
    <default>
        <max_memory_usage>**********</max_memory_usage>
        <max_memory_usage_for_user>10000000000</max_memory_usage_for_user>
    </default>
</profiles>
```

#### 4. Grafana 问题

##### 问题：面板显示 "No data"
```bash
# 检查 Grafana 日志
sudo tail -f /var/log/grafana/grafana.log

# 检查数据源连接
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:3000/api/datasources/1/health
```

##### 解决方案：
1. 验证查询语法
2. 检查时间范围
3. 确认数据源配置
4. 验证用户权限

### 监控和诊断工具

#### 1. ClickHouse 系统表
```sql
-- 查询性能监控
SELECT * FROM system.metrics;
SELECT * FROM system.events;
SELECT * FROM system.asynchronous_metrics;

-- 查询历史
SELECT * FROM system.query_log 
WHERE event_time >= now() - INTERVAL 1 HOUR;

-- 表信息
SELECT * FROM system.tables WHERE database = 'monitoring';
SELECT * FROM system.columns WHERE database = 'monitoring';
```

#### 2. Grafana API
```bash
# 获取数据源列表
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:3000/api/datasources

# 获取仪表板列表
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:3000/api/search

# 测试查询
curl -X POST \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"queries":[{"queryType":"","refId":"A","datasource":{"type":"clickhouse","uid":"YOUR_DS_UID"},"rawSql":"SELECT 1"}]}' \
     http://localhost:3000/api/ds/query
```

### 日志分析

#### ClickHouse 日志
```bash
# 主要日志文件
/var/log/clickhouse-server/clickhouse-server.log
/var/log/clickhouse-server/clickhouse-server.err.log

# 查看错误日志
sudo grep -i error /var/log/clickhouse-server/clickhouse-server.log

# 查看慢查询
sudo grep -i "slow" /var/log/clickhouse-server/clickhouse-server.log
```

#### Grafana 日志
```bash
# 主要日志文件
/var/log/grafana/grafana.log

# 查看错误
sudo grep -i error /var/log/grafana/grafana.log

# 查看数据源相关日志
sudo grep -i "datasource" /var/log/grafana/grafana.log
```

## 总结

本指南详细介绍了 Grafana 接入 ClickHouse 的完整流程，包括：

1. **环境准备和安装**: 详细的安装步骤和配置要求
2. **数据源配置**: 完整的连接配置和认证设置
3. **可视化创建**: 多种图表类型和配置选项
4. **性能优化**: 查询优化和系统调优技巧
5. **监控告警**: 完整的告警策略和通知配置
6. **最佳实践**: 生产环境的推荐做法
7. **故障排除**: 常见问题的诊断和解决方案

通过遵循本指南，您可以构建一个高性能、可扩展的监控和分析平台，充分发挥 ClickHouse 和 Grafana 的优势。

### 下一步建议

1. **实践操作**: 按照指南逐步搭建测试环境
2. **性能测试**: 使用实际数据测试查询性能
3. **监控优化**: 根据业务需求调整监控策略
4. **团队培训**: 培训团队成员使用新的监控平台
5. **持续改进**: 定期评估和优化监控效果

### 参考资源

- [ClickHouse 官方文档](https://clickhouse.com/docs/)
- [Grafana 官方文档](https://grafana.com/docs/)
- [ClickHouse Grafana 插件](https://github.com/grafana/clickhouse-datasource)
- [性能优化指南](https://clickhouse.com/docs/en/operations/performance/)
- [监控最佳实践](https://grafana.com/docs/grafana/latest/best-practices/) 